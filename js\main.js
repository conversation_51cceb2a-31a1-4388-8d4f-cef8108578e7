// Main JavaScript file for Instagram clone functionality

// Global state
let currentUser = {
    id: 0,
    username: 'john_doe_luxury',
    fullName: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face'
};

let posts = [...window.sampleData.posts];
let stories = [...window.sampleData.stories];
let isLoading = false;

// DOM Elements
const storiesContainer = document.getElementById('storiesContainer');
const postsFeed = document.getElementById('postsFeed');
const suggestionsList = document.getElementById('suggestionsList');
const trendingList = document.getElementById('trendingList');
const modalOverlay = document.getElementById('modalOverlay');
const leftSidebar = document.getElementById('leftSidebar');
const mobileMenuToggle = document.getElementById('mobileMenuToggle');

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadContent();
});

function initializeApp() {
    console.log('🌟 Luxgram initialized');
    
    // Add loading spinner styles
    addLoadingStyles();
    
    // Add empty state styles
    addEmptyStateStyles();
    
    // Setup intersection observer for infinite scroll
    setupInfiniteScroll();
}

function setupEventListeners() {
    // Mobile menu toggle
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }
    
    // Modal overlay click to close
    if (modalOverlay) {
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                closeAllModals();
            }
        });
    }
    
    // Navigation items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.dataset.page;
            if (page) {
                navigateToPage(page);
            }
        });
    });
    
    // Header buttons
    document.getElementById('homeBtn')?.addEventListener('click', () => navigateToPage('home'));
    document.getElementById('messagesBtn')?.addEventListener('click', () => navigateToPage('messages'));
    document.getElementById('addPostBtn')?.addEventListener('click', () => openCreatePostModal());
    document.getElementById('exploreBtn')?.addEventListener('click', () => navigateToPage('explore'));
    document.getElementById('notificationsBtn')?.addEventListener('click', () => navigateToPage('notifications'));
    document.getElementById('profileBtn')?.addEventListener('click', () => navigateToPage('profile'));
    
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        searchInput.addEventListener('focus', showSearchSuggestions);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Window resize handler
    window.addEventListener('resize', handleWindowResize);
}

function loadContent() {
    loadStories();
    loadPosts();
    loadSuggestions();
    loadTrending();
}

// Stories functionality
function loadStories() {
    if (!storiesContainer) return;
    
    let storiesHTML = UIComponents.createStoryItem(null, true); // Add story button
    
    stories.forEach(story => {
        storiesHTML += UIComponents.createStoryItem(story);
    });
    
    storiesContainer.innerHTML = storiesHTML;
    
    // Add horizontal scroll functionality
    setupHorizontalScroll(storiesContainer);
}

function setupHorizontalScroll(container) {
    let isDown = false;
    let startX;
    let scrollLeft;
    
    container.addEventListener('mousedown', (e) => {
        isDown = true;
        container.classList.add('active');
        startX = e.pageX - container.offsetLeft;
        scrollLeft = container.scrollLeft;
    });
    
    container.addEventListener('mouseleave', () => {
        isDown = false;
        container.classList.remove('active');
    });
    
    container.addEventListener('mouseup', () => {
        isDown = false;
        container.classList.remove('active');
    });
    
    container.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - container.offsetLeft;
        const walk = (x - startX) * 2;
        container.scrollLeft = scrollLeft - walk;
    });
}

// Posts functionality
function loadPosts() {
    if (!postsFeed) return;
    
    if (posts.length === 0) {
        postsFeed.innerHTML = UIComponents.createEmptyState(
            'camera',
            'No posts yet',
            'Start following people to see their posts in your feed.',
            'Explore',
            'navigateToPage("explore")'
        );
        return;
    }
    
    let postsHTML = '';
    posts.forEach(post => {
        postsHTML += UIComponents.createPostCard(post);
    });
    
    postsFeed.innerHTML = postsHTML;
}

// Post interactions
function toggleLike(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    const likeBtn = document.querySelector(`[data-post-id="${postId}"].action-btn`);
    const likesCount = document.getElementById(`likes-${postId}`);
    
    if (post.isLiked) {
        post.likes--;
        post.isLiked = false;
        likeBtn.classList.remove('liked');
        UIComponents.createToast('Post unliked', 'info');
    } else {
        post.likes++;
        post.isLiked = true;
        likeBtn.classList.add('liked');
        AnimationUtils.heartAnimation(likeBtn);
        UIComponents.createToast('Post liked! ❤️', 'success');
    }
    
    likesCount.innerHTML = `<strong>${window.sampleData.formatNumber(post.likes)} likes</strong>`;
}

function toggleSave(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    const saveBtn = document.querySelector(`[data-post-id="${postId}"].action-btn:last-child`);
    
    if (post.isSaved) {
        post.isSaved = false;
        saveBtn.classList.remove('saved');
        UIComponents.createToast('Post removed from saved', 'info');
    } else {
        post.isSaved = true;
        saveBtn.classList.add('saved');
        UIComponents.createToast('Post saved! 📌', 'success');
    }
}

function sharePost(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    // Create share modal content
    const shareContent = `
        <div class="share-options">
            <h4>Share this post</h4>
            <div class="share-methods">
                <button class="share-btn" onclick="shareToStory(${postId})">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add to story</span>
                </button>
                <button class="share-btn" onclick="sendDirect(${postId})">
                    <i class="fas fa-paper-plane"></i>
                    <span>Send in direct</span>
                </button>
                <button class="share-btn" onclick="copyLink(${postId})">
                    <i class="fas fa-link"></i>
                    <span>Copy link</span>
                </button>
                <button class="share-btn" onclick="shareExternal(${postId})">
                    <i class="fas fa-share"></i>
                    <span>Share to...</span>
                </button>
            </div>
        </div>
    `;
    
    showModal('shareModal', 'Share', shareContent);
}

function showComments(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    // Create comments modal content
    const commentsContent = `
        <div class="comments-container">
            <div class="post-preview">
                <img src="${post.image}" alt="Post" class="comment-post-image">
            </div>
            <div class="comments-section">
                <div class="comments-header">
                    <div class="post-user-info">
                        <img src="${post.userAvatar}" alt="${post.username}" class="post-avatar">
                        <div class="post-user-details">
                            <h4>${post.username}</h4>
                            <p>${post.location || ''}</p>
                        </div>
                    </div>
                </div>
                <div class="comments-list">
                    <div class="comment-item">
                        <img src="${post.userAvatar}" alt="${post.username}" class="comment-avatar">
                        <div class="comment-content">
                            <div class="comment-text">
                                <strong>${post.username}</strong> ${post.caption}
                            </div>
                            <div class="comment-meta">
                                <span>${window.sampleData.formatTimeAgo(post.timestamp)}</span>
                            </div>
                        </div>
                    </div>
                    <!-- Sample comments -->
                    <div class="comment-item">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" alt="User" class="comment-avatar">
                        <div class="comment-content">
                            <div class="comment-text">
                                <strong>luxury_lover</strong> Absolutely stunning! 😍✨
                            </div>
                            <div class="comment-meta">
                                <span>2h</span>
                                <button class="comment-action">Reply</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="add-comment-modal">
                    <input type="text" placeholder="Add a comment..." id="modalCommentInput">
                    <button class="post-btn" onclick="submitModalComment(${postId})">Post</button>
                </div>
            </div>
        </div>
    `;
    
    showModal('commentsModal', 'Comments', commentsContent, 'comments-modal');
}

// Suggestions and trending
function loadSuggestions() {
    if (!suggestionsList) return;

    let suggestionsHTML = '';
    window.sampleData.suggestions.forEach(suggestion => {
        suggestionsHTML += UIComponents.createSuggestionItem(suggestion);
    });

    suggestionsList.innerHTML = suggestionsHTML;
}

function loadTrending() {
    if (!trendingList) return;

    let trendingHTML = '';
    window.sampleData.trending.forEach(hashtag => {
        trendingHTML += UIComponents.createTrendingItem(hashtag);
    });

    trendingList.innerHTML = trendingHTML;
}

// Modal functionality
function showModal(id, title, content, className = '') {
    const modal = UIComponents.createModal(id, title, content, className);
    modalOverlay.innerHTML = modal;
    modalOverlay.style.display = 'flex';
    AnimationUtils.fadeIn(modalOverlay);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeModal(id) {
    AnimationUtils.fadeOut(modalOverlay);
    setTimeout(() => {
        modalOverlay.style.display = 'none';
        modalOverlay.innerHTML = '';
        document.body.style.overflow = '';
    }, 300);
}

function closeAllModals() {
    closeModal();
}

// Navigation
function navigateToPage(page) {
    // Update active nav item
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.page === page) {
            item.classList.add('active');
        }
    });

    // Handle page-specific logic
    switch (page) {
        case 'home':
            loadPosts();
            UIComponents.createToast('Welcome home! 🏠', 'success');
            break;
        case 'search':
            showSearchPage();
            break;
        case 'explore':
            showExplorePage();
            break;
        case 'reels':
            showReelsPage();
            break;
        case 'messages':
            showMessagesPage();
            break;
        case 'notifications':
            showNotificationsPage();
            break;
        case 'create':
            openCreatePostModal();
            break;
        case 'profile':
            showProfilePage();
            break;
    }
}

// Mobile menu
function toggleMobileMenu() {
    const overlay = document.querySelector('.mobile-overlay') || createMobileOverlay();

    if (leftSidebar.classList.contains('active')) {
        leftSidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    } else {
        leftSidebar.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function createMobileOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'mobile-overlay';
    overlay.addEventListener('click', toggleMobileMenu);
    document.body.appendChild(overlay);
    return overlay;
}

// Search functionality
function handleSearch(event) {
    const query = event.target.value.trim();
    if (query.length < 2) return;

    console.log('Searching for:', query);
    // Implement search logic here
    UIComponents.createToast(`Searching for "${query}"...`, 'info');
}

function showSearchSuggestions() {
    // Show search suggestions dropdown
    console.log('Showing search suggestions');
}

function searchHashtag(tag) {
    console.log('Searching hashtag:', tag);
    UIComponents.createToast(`Searching #${tag}`, 'info');
}

// Create post modal
function openCreatePostModal() {
    const createContent = `
        <div class="create-post-container">
            <div class="create-step" id="createStep1">
                <div class="upload-area" onclick="triggerFileUpload()">
                    <i class="fas fa-camera"></i>
                    <h3>Select photos and videos</h3>
                    <p>Drag photos and videos here</p>
                    <button class="btn btn-primary">Select from computer</button>
                </div>
                <input type="file" id="fileInput" accept="image/*,video/*" multiple style="display: none;">
            </div>
            <div class="create-step" id="createStep2" style="display: none;">
                <div class="image-preview">
                    <img id="previewImage" src="" alt="Preview">
                </div>
                <div class="post-details">
                    <div class="user-info">
                        <img src="${currentUser.avatar}" alt="${currentUser.username}" class="user-avatar">
                        <span>${currentUser.username}</span>
                    </div>
                    <textarea placeholder="Write a caption..." id="captionInput"></textarea>
                    <input type="text" placeholder="Add location" id="locationInput">
                    <div class="post-options">
                        <label>
                            <input type="checkbox" id="hideComments"> Hide like and view counts
                        </label>
                        <label>
                            <input type="checkbox" id="turnOffComments"> Turn off commenting
                        </label>
                    </div>
                    <button class="btn btn-primary" onclick="publishPost()">Share</button>
                </div>
            </div>
        </div>
    `;

    showModal('createPostModal', 'Create new post', createContent, 'create-post-modal');
}

function triggerFileUpload() {
    document.getElementById('fileInput').click();
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function handleKeyboardShortcuts(event) {
    // Escape key to close modals
    if (event.key === 'Escape') {
        closeAllModals();
        if (leftSidebar.classList.contains('active')) {
            toggleMobileMenu();
        }
    }

    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        document.getElementById('searchInput')?.focus();
    }
}

function handleWindowResize() {
    // Handle responsive behavior on window resize
    if (window.innerWidth > 768 && leftSidebar.classList.contains('active')) {
        toggleMobileMenu();
    }
}

// Additional functionality
function setupInfiniteScroll() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !isLoading) {
                loadMorePosts();
            }
        });
    }, { threshold: 0.1 });

    // Create a sentinel element at the bottom of the feed
    const sentinel = document.createElement('div');
    sentinel.id = 'scroll-sentinel';
    sentinel.style.height = '20px';

    if (postsFeed) {
        postsFeed.appendChild(sentinel);
        observer.observe(sentinel);
    }
}

function loadMorePosts() {
    if (isLoading) return;

    isLoading = true;
    const loadingSpinner = document.createElement('div');
    loadingSpinner.innerHTML = UIComponents.createLoadingSpinner();
    loadingSpinner.id = 'loading-more';
    postsFeed.appendChild(loadingSpinner);

    // Simulate API call
    setTimeout(() => {
        // Add more sample posts (duplicate existing ones for demo)
        const morePosts = window.sampleData.posts.map(post => ({
            ...post,
            id: post.id + posts.length,
            timestamp: new Date(Date.now() - Math.random() * 86400000) // Random time within last day
        }));

        posts.push(...morePosts);

        // Remove loading spinner
        document.getElementById('loading-more')?.remove();

        // Add new posts to feed
        let newPostsHTML = '';
        morePosts.forEach(post => {
            newPostsHTML += UIComponents.createPostCard(post);
        });

        const sentinel = document.getElementById('scroll-sentinel');
        sentinel.insertAdjacentHTML('beforebegin', newPostsHTML);

        isLoading = false;
        UIComponents.createToast('New posts loaded! 📱', 'success');
    }, 1500);
}

// Page-specific functions
function showSearchPage() {
    UIComponents.createToast('Search page - Coming soon! 🔍', 'info');
}

function showExplorePage() {
    UIComponents.createToast('Explore page - Coming soon! 🧭', 'info');
}

function showReelsPage() {
    UIComponents.createToast('Reels page - Coming soon! 🎬', 'info');
}

function showMessagesPage() {
    UIComponents.createToast('Messages page - Coming soon! 💬', 'info');
}

function showNotificationsPage() {
    UIComponents.createToast('Notifications page - Coming soon! 🔔', 'info');
}

function showProfilePage() {
    UIComponents.createToast('Profile page - Coming soon! 👤', 'info');
}

// Post creation functions
function publishPost() {
    const caption = document.getElementById('captionInput')?.value || '';
    const location = document.getElementById('locationInput')?.value || '';

    if (!caption.trim()) {
        UIComponents.createToast('Please add a caption', 'error');
        return;
    }

    // Create new post
    const newPost = {
        id: posts.length + 1,
        userId: currentUser.id,
        username: currentUser.username,
        userAvatar: currentUser.avatar,
        location: location,
        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=600&fit=crop',
        caption: caption,
        likes: 0,
        comments: 0,
        shares: 0,
        timestamp: new Date(),
        isLiked: false,
        isSaved: false
    };

    posts.unshift(newPost); // Add to beginning of array
    loadPosts(); // Reload posts
    closeAllModals();

    UIComponents.createToast('Post published successfully! 🎉', 'success');
}

// Comment functions
function handleCommentSubmit(event, postId) {
    if (event.key === 'Enter') {
        submitComment(postId);
    }
}

function submitComment(postId) {
    const input = event.target.previousElementSibling || document.querySelector(`input[placeholder="Add a comment..."]`);
    const comment = input.value.trim();

    if (!comment) return;

    const post = posts.find(p => p.id === postId);
    if (post) {
        post.comments++;
        input.value = '';
        UIComponents.createToast('Comment added! 💬', 'success');
    }
}

function submitModalComment(postId) {
    const input = document.getElementById('modalCommentInput');
    const comment = input.value.trim();

    if (!comment) return;

    const post = posts.find(p => p.id === postId);
    if (post) {
        post.comments++;
        input.value = '';
        UIComponents.createToast('Comment added! 💬', 'success');
    }
}

// Follow/unfollow functions
function followUser(userId) {
    UIComponents.createToast('Following user! ✨', 'success');

    // Remove from suggestions
    const suggestionElement = event.target.closest('.suggestion-item');
    if (suggestionElement) {
        AnimationUtils.slideUp(suggestionElement);
    }
}

// Story functions
function viewStory(storyId) {
    const story = stories.find(s => s.id === storyId);
    if (story) {
        story.isViewed = true;
        loadStories(); // Reload to update visual state
        UIComponents.createToast(`Viewing ${story.username}'s story`, 'info');
    }
}

function openCreateStoryModal() {
    UIComponents.createToast('Create story - Coming soon! 📸', 'info');
}

// Share functions
function shareToStory(postId) {
    UIComponents.createToast('Added to story! 📱', 'success');
    closeAllModals();
}

function sendDirect(postId) {
    UIComponents.createToast('Sent in direct message! 📩', 'success');
    closeAllModals();
}

function copyLink(postId) {
    // Simulate copying link to clipboard
    const link = `https://luxgram.com/p/${postId}`;
    navigator.clipboard?.writeText(link);
    UIComponents.createToast('Link copied to clipboard! 🔗', 'success');
    closeAllModals();
}

function shareExternal(postId) {
    if (navigator.share) {
        navigator.share({
            title: 'Check out this post on Luxgram',
            url: `https://luxgram.com/p/${postId}`
        });
    } else {
        UIComponents.createToast('Sharing - Coming soon! 📤', 'info');
    }
    closeAllModals();
}

// Post menu functions
function showPostMenu(postId) {
    const menuContent = `
        <div class="post-menu">
            <button class="menu-item" onclick="reportPost(${postId})">
                <i class="fas fa-flag"></i>
                <span>Report</span>
            </button>
            <button class="menu-item" onclick="unfollowUser(${postId})">
                <i class="fas fa-user-times"></i>
                <span>Unfollow</span>
            </button>
            <button class="menu-item" onclick="copyPostLink(${postId})">
                <i class="fas fa-link"></i>
                <span>Copy link</span>
            </button>
            <button class="menu-item cancel" onclick="closeAllModals()">
                <span>Cancel</span>
            </button>
        </div>
    `;

    showModal('postMenuModal', '', menuContent, 'post-menu-modal');
}

function reportPost(postId) {
    UIComponents.createToast('Post reported', 'info');
    closeAllModals();
}

function unfollowUser(postId) {
    UIComponents.createToast('User unfollowed', 'info');
    closeAllModals();
}

function copyPostLink(postId) {
    copyLink(postId);
}

// Style injection functions
function addLoadingStyles() {
    if (document.querySelector('#loading-styles')) return;

    const loadingStyles = document.createElement('style');
    loadingStyles.id = 'loading-styles';
    loadingStyles.textContent = `
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--medium-gray);
            border-top: 3px solid var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .spinner-sm .spinner {
            width: 20px;
            height: 20px;
            border-width: 2px;
        }

        .spinner-lg .spinner {
            width: 60px;
            height: 60px;
            border-width: 4px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(loadingStyles);
}

function addEmptyStateStyles() {
    if (document.querySelector('#empty-state-styles')) return;

    const emptyStateStyles = document.createElement('style');
    emptyStateStyles.id = 'empty-state-styles';
    emptyStateStyles.textContent = `
        .empty-state {
            text-align: center;
            padding: var(--spacing-2xl);
            color: var(--text-muted);
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            color: var(--primary-gold);
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-md);
            color: var(--text-secondary);
        }

        .empty-state-description {
            font-size: 1rem;
            margin-bottom: var(--spacing-xl);
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .modal {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--glass-border);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 1.25rem;
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .close-btn:hover {
            color: var(--text-primary);
            background: var(--medium-gray);
        }

        .modal-content {
            padding: var(--spacing-lg);
            overflow-y: auto;
            max-height: calc(80vh - 80px);
        }

        /* Comments modal specific styles */
        .comments-modal {
            max-width: 800px;
            height: 80vh;
        }

        .comments-container {
            display: flex;
            height: 100%;
        }

        .post-preview {
            flex: 1;
            background: var(--primary-black);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .comment-post-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .comments-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-left: 1px solid var(--glass-border);
        }

        .comments-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--glass-border);
        }

        .comments-list {
            flex: 1;
            overflow-y: auto;
            padding: var(--spacing-md);
        }

        .comment-item {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            object-fit: cover;
        }

        .comment-content {
            flex: 1;
        }

        .comment-text {
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: var(--spacing-xs);
        }

        .comment-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .comment-action {
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 0.8rem;
            transition: color var(--transition-fast);
        }

        .comment-action:hover {
            color: var(--text-secondary);
        }

        .add-comment-modal {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            border-top: 1px solid var(--glass-border);
        }

        .add-comment-modal input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 0.9rem;
            outline: none;
        }

        .add-comment-modal input::placeholder {
            color: var(--text-muted);
        }

        /* Share modal styles */
        .share-options h4 {
            text-align: center;
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
        }

        .share-methods {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .share-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: none;
            border: none;
            color: var(--text-primary);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: left;
        }

        .share-btn:hover {
            background: var(--medium-gray);
        }

        .share-btn i {
            font-size: 1.25rem;
            color: var(--primary-gold);
        }

        /* Post menu styles */
        .post-menu-modal .modal {
            max-width: 300px;
        }

        .post-menu {
            display: flex;
            flex-direction: column;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: left;
            border-bottom: 1px solid var(--glass-border);
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: var(--medium-gray);
        }

        .menu-item.cancel {
            color: var(--text-muted);
            justify-content: center;
        }

        .menu-item i {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        /* Create post modal styles */
        .create-post-modal {
            max-width: 800px;
            height: 80vh;
        }

        .create-post-container {
            height: 100%;
        }

        .create-step {
            height: 100%;
        }

        .upload-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            border: 2px dashed var(--glass-border);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .upload-area:hover {
            border-color: var(--primary-gold);
            background: var(--glass-bg);
        }

        .upload-area i {
            font-size: 3rem;
            color: var(--primary-gold);
            margin-bottom: var(--spacing-lg);
        }

        .upload-area h3 {
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .upload-area p {
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
        }
    `;
    document.head.appendChild(emptyStateStyles);
}

// Initialize everything when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Luxgram fully loaded and ready!');
    });
} else {
    console.log('🚀 Luxgram fully loaded and ready!');
}
