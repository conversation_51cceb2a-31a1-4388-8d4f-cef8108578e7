/* ===== SIDEBAR COMPONENTS ===== */
.sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.left-sidebar {
    width: 250px;
    min-width: 250px;
}

.right-sidebar {
    width: 320px;
    min-width: 320px;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background: var(--medium-gray);
    color: var(--text-primary);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    color: var(--primary-black);
    font-weight: 600;
}

.nav-item i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.nav-profile-pic {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
}

/* ===== FEED CONTAINER ===== */
.feed-container {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== STORIES SECTION ===== */
.stories-section {
    margin-bottom: var(--spacing-xl);
}

.stories-container {
    display: flex;
    gap: var(--spacing-md);
    overflow-x: auto;
    padding: var(--spacing-md);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.stories-container::-webkit-scrollbar {
    display: none;
}

.story-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 80px;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.story-item:hover {
    transform: scale(1.05);
}

.story-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-full);
    padding: 3px;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    position: relative;
}

.story-avatar.viewed {
    background: var(--text-gray);
}

.story-avatar img {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--primary-black);
}

.story-username {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.add-story {
    position: relative;
}

.add-story-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: var(--primary-gold);
    color: var(--primary-black);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

/* ===== POST COMPONENTS ===== */
.post-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    transition: transform var(--transition-normal);
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
}

.post-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
    object-fit: cover;
}

.post-user-details h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.post-user-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

.post-menu-btn {
    color: var(--text-muted);
    font-size: 1.25rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.post-menu-btn:hover {
    color: var(--primary-gold);
    background: var(--medium-gray);
}

.post-image {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
    cursor: pointer;
    transition: transform var(--transition-slow);
}

.post-image:hover {
    transform: scale(1.02);
}

.post-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
}

.post-actions-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    color: var(--text-primary);
    font-size: 1.5rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.action-btn:hover {
    color: var(--primary-gold);
    transform: scale(1.1);
}

.action-btn.liked {
    color: #ff3040;
}

.action-btn.saved {
    color: var(--primary-gold);
}

.post-stats {
    padding: 0 var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.likes-count {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.post-caption {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.post-caption .username {
    font-weight: 600;
    margin-right: var(--spacing-xs);
}

.view-comments {
    color: var(--text-muted);
    font-size: 0.9rem;
    cursor: pointer;
    margin-bottom: var(--spacing-xs);
}

.view-comments:hover {
    color: var(--text-secondary);
}

.post-time {
    color: var(--text-muted);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.comment-section {
    border-top: 1px solid var(--glass-border);
    padding: var(--spacing-md);
}

.add-comment {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.add-comment input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
}

.add-comment input::placeholder {
    color: var(--text-muted);
}

.post-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.9rem;
    opacity: 0.5;
    transition: opacity var(--transition-fast);
}

.post-btn.active {
    opacity: 1;
    cursor: pointer;
}

/* ===== RIGHT SIDEBAR COMPONENTS ===== */
.profile-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.profile-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.profile-pic {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
    object-fit: cover;
}

.profile-details h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.profile-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

.switch-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.switch-btn:hover {
    background: var(--medium-gray);
}

.suggestions-section,
.trending-section {
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.section-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-muted);
    margin: 0;
}

.see-all {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.suggestion-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.suggestion-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 1px solid var(--glass-border);
    object-fit: cover;
}

.suggestion-details h5 {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.suggestion-details p {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

.follow-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.follow-btn:hover {
    background: var(--medium-gray);
}

.trending-item {
    padding: var(--spacing-xs) 0;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.trending-item:hover {
    color: var(--primary-gold);
}

.hashtag {
    color: var(--primary-gold);
    font-weight: 600;
    margin-right: var(--spacing-xs);
}

.sidebar-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-links a {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.copyright {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

/* ===== COMMENT SYSTEM STYLES ===== */
.comment-item.reply {
    margin-left: var(--spacing-xl);
    border-left: 2px solid var(--glass-border);
    padding-left: var(--spacing-md);
}

.comment-item.original-post {
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.comment-action {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 0.8rem;
    transition: color var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.comment-action:hover {
    color: var(--text-secondary);
}

.comment-action i.fa-heart.liked {
    color: #ff3040;
}

/* ===== NOTIFICATIONS STYLES ===== */
.notifications-modal {
    max-width: 600px;
    height: 80vh;
}

.notifications-page {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: var(--spacing-md);
}

.notifications-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.mark-all-read-btn {
    background: none;
    border: none;
    color: var(--primary-gold);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.mark-all-read-btn:hover {
    background: var(--medium-gray);
}

.notifications-list {
    flex: 1;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.notification-item:hover {
    background: var(--medium-gray);
}

.notification-item.unread {
    background: var(--glass-bg);
    border-left: 3px solid var(--primary-gold);
}

.notification-avatar {
    position: relative;
}

.notification-avatar img {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.notification-icon {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: var(--primary-black);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-black);
}

.notification-icon i {
    font-size: 0.7rem;
    color: var(--primary-gold);
}

.notification-icon i.liked {
    color: #ff3040;
}

.notification-content {
    flex: 1;
}

.notification-text {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.notification-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.notification-post-image {
    width: 44px;
    height: 44px;
}

.notification-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.notification-post-image img:hover {
    transform: scale(1.05);
}

/* ===== MESSAGES STYLES ===== */
.messages-modal {
    max-width: 1000px;
    width: 95%;
    height: 80vh;
}

.messages-page {
    display: flex;
    height: 100%;
}

.messages-sidebar {
    width: 350px;
    border-right: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
}

.messages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
}

.messages-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.new-message-btn {
    background: none;
    border: none;
    color: var(--primary-gold);
    cursor: pointer;
    font-size: 1.25rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.new-message-btn:hover {
    background: var(--medium-gray);
}

.messages-list {
    flex: 1;
    overflow-y: auto;
}

.conversation-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.conversation-item:hover {
    background: var(--medium-gray);
}

.conversation-item.unread {
    background: var(--glass-bg);
}

.conversation-avatar {
    position: relative;
}

.conversation-avatar img {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.unread-indicator {
    position: absolute;
    top: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--primary-gold);
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-black);
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.conversation-header h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.conversation-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.conversation-preview {
    font-size: 0.85rem;
    color: var(--text-muted);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-preview .unread-text {
    color: var(--text-primary);
    font-weight: 500;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-muted);
}

.no-chat-selected i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-gold);
    opacity: 0.5;
}

.no-chat-selected h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.chat-user-details h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.chat-user-details p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.chat-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.chat-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.chat-action-btn:hover {
    color: var(--primary-gold);
    background: var(--medium-gray);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.message-item {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    max-width: 70%;
}

.message-item.own {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-item.other {
    align-self: flex-start;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.message-item.own .message-content {
    align-items: flex-end;
}

.message-bubble {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-item.own .message-bubble {
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    color: var(--primary-black);
}

.message-item.other .message-bubble {
    background: var(--medium-gray);
    color: var(--text-primary);
}

.message-time {
    font-size: 0.7rem;
    color: var(--text-muted);
}

.chat-input-area {
    padding: var(--spacing-md);
    border-top: 1px solid var(--glass-border);
}

.chat-input-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--medium-gray);
    border-radius: var(--radius-xl);
    padding: var(--spacing-sm);
}

.chat-emoji-btn,
.chat-send-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.chat-emoji-btn:hover,
.chat-send-btn:hover {
    color: var(--primary-gold);
}

.chat-input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
    padding: var(--spacing-xs);
}

.chat-input::placeholder {
    color: var(--text-muted);
}

/* ===== NEW MESSAGE MODAL STYLES ===== */
.new-message-modal {
    max-width: 500px;
    height: 70vh;
}

.new-message-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.new-message-header {
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: var(--spacing-md);
}

.new-message-header h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
}

.search-users input {
    width: 100%;
    background: var(--medium-gray);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
}

.search-users input::placeholder {
    color: var(--text-muted);
}

.users-list {
    flex: 1;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.user-item:hover {
    background: var(--medium-gray);
}

.user-avatar {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.user-info h5 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.user-info p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.no-results {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
}

/* ===== CREATE POST ENHANCED STYLES ===== */
.create-post-modal {
    max-width: 1000px;
    width: 95%;
    height: 85vh;
}

.create-post-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.create-step {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    border: 2px dashed var(--glass-border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: center;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--primary-gold);
    background: var(--glass-bg);
}

.upload-area i {
    font-size: 3rem;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-lg);
}

.upload-area h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.upload-area p {
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

#createStep2 {
    flex-direction: row;
    gap: var(--spacing-lg);
}

.image-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.image-preview-container {
    position: relative;
    flex: 1;
    background: var(--primary-black);
    border-radius: var(--radius-md);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

#previewImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: all var(--transition-normal);
}

.image-controls {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
}

.control-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: var(--text-primary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.control-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    color: var(--primary-gold);
}

.filters-section {
    margin-top: var(--spacing-md);
}

.filters-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filters-list {
    display: flex;
    gap: var(--spacing-sm);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
}

.filter-item {
    min-width: 80px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-item:hover {
    transform: scale(1.05);
}

.filter-item.active .filter-preview {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 2px var(--gold-glow);
}

.filter-preview {
    width: 60px;
    height: 60px;
    background: var(--medium-gray);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    transition: all var(--transition-fast);
}

.post-details {
    width: 350px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-left: 1px solid var(--glass-border);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

#captionInput {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    resize: none;
    outline: none;
    min-height: 100px;
    font-family: inherit;
}

#captionInput::placeholder {
    color: var(--text-muted);
}

.caption-counter {
    text-align: right;
    font-size: 0.8rem;
    color: var(--text-muted);
}

#locationInput,
#altTextInput {
    background: none;
    border: none;
    border-bottom: 1px solid var(--glass-border);
    color: var(--text-primary);
    font-size: 0.9rem;
    padding: var(--spacing-sm) 0;
    outline: none;
    transition: border-color var(--transition-fast);
}

#locationInput:focus,
#altTextInput:focus {
    border-bottom-color: var(--primary-gold);
}

#locationInput::placeholder,
#altTextInput::placeholder {
    color: var(--text-muted);
}

.accessibility-section,
.advanced-settings {
    border-top: 1px solid var(--glass-border);
    padding-top: var(--spacing-md);
}

.accessibility-section h5,
.advanced-settings h5 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.help-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    margin-bottom: 0;
}

.post-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.option-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    font-size: 0.9rem;
}

.option-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-gold);
}

.publish-section {
    display: flex;
    gap: var(--spacing-md);
    margin-top: auto;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--glass-border);
}

.publish-section .btn {
    flex: 1;
}
