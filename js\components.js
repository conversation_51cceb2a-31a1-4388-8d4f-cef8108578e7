// UI Components for the Instagram clone

class UIComponents {
    // Create a story item
    static createStoryItem(story, isAddStory = false) {
        if (isAddStory) {
            return `
                <div class="story-item add-story" onclick="openCreateStoryModal()">
                    <div class="story-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face" alt="Your story">
                        <div class="add-story-btn">
                            <i class="fas fa-plus"></i>
                        </div>
                    </div>
                    <span class="story-username">Your story</span>
                </div>
            `;
        }
        
        return `
            <div class="story-item" onclick="viewStory(${story.id})">
                <div class="story-avatar ${story.isViewed ? 'viewed' : ''}">
                    <img src="${story.avatar}" alt="${story.username}">
                </div>
                <span class="story-username">${story.username}</span>
            </div>
        `;
    }

    // Create a post card
    static createPostCard(post) {
        return `
            <article class="post-card">
                <header class="post-header">
                    <div class="post-user-info">
                        <img src="${post.userAvatar}" alt="${post.username}" class="post-avatar">
                        <div class="post-user-details">
                            <h4>${post.username}</h4>
                            <p>${post.location || ''}</p>
                        </div>
                    </div>
                    <button class="post-menu-btn" onclick="showPostMenu(${post.id})">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </header>
                
                <div class="post-image-container">
                    <img src="${post.image}" alt="Post image" class="post-image" ondblclick="toggleLike(${post.id})">
                </div>
                
                <div class="post-actions">
                    <div class="post-actions-left">
                        <button class="action-btn ${post.isLiked ? 'liked' : ''}" onclick="toggleLike(${post.id})" data-post-id="${post.id}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn" onclick="showComments(${post.id})">
                            <i class="fas fa-comment"></i>
                        </button>
                        <button class="action-btn" onclick="sharePost(${post.id})">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <button class="action-btn ${post.isSaved ? 'saved' : ''}" onclick="toggleSave(${post.id})" data-post-id="${post.id}">
                        <i class="fas fa-bookmark"></i>
                    </button>
                </div>
                
                <div class="post-stats">
                    <div class="likes-count" id="likes-${post.id}">
                        <strong>${window.sampleData.formatNumber(post.likes)} likes</strong>
                    </div>
                    <div class="post-caption">
                        <span class="username">${post.username}</span>
                        ${post.caption}
                    </div>
                    ${post.comments > 0 ? `
                        <div class="view-comments" onclick="showComments(${post.id})">
                            View all ${post.comments} comments
                        </div>
                    ` : ''}
                    <div class="post-time">${window.sampleData.formatTimeAgo(post.timestamp)}</div>
                </div>
                
                <div class="comment-section">
                    <div class="add-comment">
                        <input type="text" placeholder="Add a comment..." onkeypress="handleCommentSubmit(event, ${post.id})">
                        <button class="post-btn" onclick="submitComment(${post.id})">Post</button>
                    </div>
                </div>
            </article>
        `;
    }

    // Create a suggestion item
    static createSuggestionItem(suggestion) {
        return `
            <div class="suggestion-item">
                <div class="suggestion-info">
                    <img src="${suggestion.avatar}" alt="${suggestion.username}" class="suggestion-avatar">
                    <div class="suggestion-details">
                        <h5>${suggestion.username}</h5>
                        <p>${suggestion.reason}</p>
                    </div>
                </div>
                <button class="follow-btn" onclick="followUser(${suggestion.id})">Follow</button>
            </div>
        `;
    }

    // Create a trending hashtag item
    static createTrendingItem(hashtag) {
        return `
            <div class="trending-item" onclick="searchHashtag('${hashtag.tag}')">
                <span class="hashtag">#${hashtag.tag}</span>
                <span>${window.sampleData.formatNumber(hashtag.posts)} posts</span>
            </div>
        `;
    }

    // Create a modal
    static createModal(id, title, content, className = '') {
        return `
            <div class="modal ${className}" id="${id}">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="close-btn" onclick="closeModal('${id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-content">
                    ${content}
                </div>
            </div>
        `;
    }

    // Create a notification toast
    static createToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add toast styles if not already added
        if (!document.querySelector('#toast-styles')) {
            const toastStyles = document.createElement('style');
            toastStyles.id = 'toast-styles';
            toastStyles.textContent = `
                .toast {
                    position: fixed;
                    top: 100px;
                    right: 20px;
                    background: var(--glass-bg);
                    backdrop-filter: blur(20px);
                    border: 1px solid var(--glass-border);
                    border-radius: var(--radius-lg);
                    padding: var(--spacing-md);
                    color: var(--text-primary);
                    z-index: 10000;
                    min-width: 300px;
                    box-shadow: var(--shadow-lg);
                    animation: slideInRight 0.3s ease;
                }
                
                .toast-success {
                    border-left: 4px solid var(--primary-gold);
                }
                
                .toast-error {
                    border-left: 4px solid #ff3040;
                }
                
                .toast-info {
                    border-left: 4px solid #3b82f6;
                }
                
                .toast-content {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                }
                
                .toast-close {
                    position: absolute;
                    top: var(--spacing-xs);
                    right: var(--spacing-xs);
                    background: none;
                    border: none;
                    color: var(--text-muted);
                    cursor: pointer;
                    padding: var(--spacing-xs);
                    border-radius: var(--radius-sm);
                    transition: all var(--transition-fast);
                }
                
                .toast-close:hover {
                    color: var(--text-primary);
                    background: var(--medium-gray);
                }
                
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(toastStyles);
        }
        
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideInRight 0.3s ease reverse';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
        
        return toast;
    }

    // Create a loading spinner
    static createLoadingSpinner(size = 'medium') {
        const sizeClass = size === 'small' ? 'spinner-sm' : size === 'large' ? 'spinner-lg' : '';
        
        return `
            <div class="loading-spinner ${sizeClass}">
                <div class="spinner"></div>
            </div>
        `;
    }

    // Create an empty state
    static createEmptyState(icon, title, description, actionText = null, actionCallback = null) {
        return `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-${icon}"></i>
                </div>
                <h3 class="empty-state-title">${title}</h3>
                <p class="empty-state-description">${description}</p>
                ${actionText && actionCallback ? `
                    <button class="btn btn-primary" onclick="${actionCallback}">${actionText}</button>
                ` : ''}
            </div>
        `;
    }
}

// Animation utilities
class AnimationUtils {
    static fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        
        let start = null;
        function animate(timestamp) {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = Math.min(progress / duration, 1);
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            }
        }
        
        requestAnimationFrame(animate);
    }

    static fadeOut(element, duration = 300) {
        let start = null;
        const initialOpacity = parseFloat(getComputedStyle(element).opacity);
        
        function animate(timestamp) {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = initialOpacity * (1 - Math.min(progress / duration, 1));
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            } else {
                element.style.display = 'none';
            }
        }
        
        requestAnimationFrame(animate);
    }

    static slideUp(element, duration = 300) {
        const height = element.offsetHeight;
        element.style.height = height + 'px';
        element.style.overflow = 'hidden';
        
        let start = null;
        function animate(timestamp) {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const newHeight = height * (1 - Math.min(progress / duration, 1));
            
            element.style.height = newHeight + 'px';
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            } else {
                element.style.display = 'none';
                element.style.height = '';
                element.style.overflow = '';
            }
        }
        
        requestAnimationFrame(animate);
    }

    static heartAnimation(element) {
        element.style.transform = 'scale(1.3)';
        element.style.transition = 'transform 0.1s ease';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 100);
    }
}

// Make components available globally
window.UIComponents = UIComponents;
window.AnimationUtils = AnimationUtils;
