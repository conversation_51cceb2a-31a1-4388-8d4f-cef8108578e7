/* ===== CSS VARIABLES & DESIGN SYSTEM ===== */
:root {
    /* Primary Colors */
    --primary-black: #000000;
    --secondary-black: #0a0a0a;
    --dark-gray: #1a1a1a;
    --medium-gray: #2a2a2a;
    --light-gray: #3a3a3a;
    --text-gray: #8e8e8e;
    
    /* Gold Colors */
    --primary-gold: #FFD700;
    --secondary-gold: #F4C430;
    --dark-gold: #B8860B;
    --gold-glow: rgba(255, 215, 0, 0.3);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #c7c7c7;
    --text-muted: #8e8e8e;
    
    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-gold: 0 0 20px var(--gold-glow);
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--dark-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-gold);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-gold);
}

/* ===== BUTTONS ===== */
button {
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
    transition: all var(--transition-fast);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-normal);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    color: var(--primary-black);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-gold);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--medium-gray);
    border-color: var(--primary-gold);
}

/* ===== GLASSMORPHISM UTILITIES ===== */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.glass-card {
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

/* ===== MAIN LAYOUT ===== */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--spacing-md) 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-gold);
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-container {
    flex: 1;
    max-width: 400px;
    margin: 0 var(--spacing-xl);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--dark-gray);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    transition: all var(--transition-normal);
}

.search-box:focus-within {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 2px var(--gold-glow);
}

.search-box i {
    color: var(--text-muted);
    margin-right: var(--spacing-sm);
}

.search-box input {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 100%;
    outline: none;
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-btn {
    position: relative;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1.25rem;
    transition: all var(--transition-fast);
}

.header-btn:hover {
    color: var(--primary-gold);
    background: var(--glass-bg);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--primary-gold);
    color: var(--primary-black);
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    border: 2px solid transparent;
    transition: border-color var(--transition-fast);
}

.profile-btn:hover .profile-avatar {
    border-color: var(--primary-gold);
}

.main-content {
    margin-top: 80px;
    display: flex;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: var(--spacing-lg);
    gap: var(--spacing-xl);
    min-height: calc(100vh - 80px);
}

/* ===== MOBILE MENU ===== */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 1001;
    background: var(--glass-bg);
    color: var(--text-primary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 1.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
}

.mobile-menu-toggle:hover {
    color: var(--primary-gold);
    background: var(--medium-gray);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .header {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-content {
        margin-top: 0;
        padding: var(--spacing-md);
        gap: 0;
    }
    
    .search-container {
        display: none;
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
}
