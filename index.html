<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxgram - Luxury Instagram Clone</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Main Container -->
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-instagram"></i>
                    <span>Luxgram</span>
                </div>
                
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search" id="searchInput">
                    </div>
                </div>
                
                <div class="header-actions">
                    <button class="header-btn" id="homeBtn">
                        <i class="fas fa-home"></i>
                    </button>
                    <button class="header-btn" id="messagesBtn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="header-btn" id="addPostBtn">
                        <i class="fas fa-plus-square"></i>
                    </button>
                    <button class="header-btn" id="exploreBtn">
                        <i class="fas fa-compass"></i>
                    </button>
                    <button class="header-btn" id="notificationsBtn">
                        <i class="fas fa-heart"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="header-btn profile-btn" id="profileBtn">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face" alt="Profile" class="profile-avatar">
                    </button>
                </div>
            </div>
        </header>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Sidebar -->
            <aside class="sidebar left-sidebar" id="leftSidebar">
                <nav class="sidebar-nav">
                    <a href="#" class="nav-item active" data-page="home">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="#" class="nav-item" data-page="search">
                        <i class="fas fa-search"></i>
                        <span>Search</span>
                    </a>
                    <a href="#" class="nav-item" data-page="explore">
                        <i class="fas fa-compass"></i>
                        <span>Explore</span>
                    </a>
                    <a href="#" class="nav-item" data-page="reels">
                        <i class="fas fa-play"></i>
                        <span>Reels</span>
                    </a>
                    <a href="#" class="nav-item" data-page="messages">
                        <i class="fas fa-paper-plane"></i>
                        <span>Messages</span>
                    </a>
                    <a href="#" class="nav-item" data-page="notifications">
                        <i class="fas fa-heart"></i>
                        <span>Notifications</span>
                    </a>
                    <a href="#" class="nav-item" data-page="create">
                        <i class="fas fa-plus-square"></i>
                        <span>Create</span>
                    </a>
                    <a href="#" class="nav-item" data-page="profile">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=24&h=24&fit=crop&crop=face" alt="Profile" class="nav-profile-pic">
                        <span>Profile</span>
                    </a>
                </nav>
            </aside>

            <!-- Center Feed -->
            <div class="feed-container">
                <!-- Stories Section -->
                <section class="stories-section">
                    <div class="stories-container" id="storiesContainer">
                        <!-- Stories will be dynamically loaded here -->
                    </div>
                </section>

                <!-- Posts Feed -->
                <section class="posts-feed" id="postsFeed">
                    <!-- Posts will be dynamically loaded here -->
                </section>
            </div>

            <!-- Right Sidebar -->
            <aside class="sidebar right-sidebar">
                <!-- User Profile Summary -->
                <div class="profile-summary">
                    <div class="profile-info">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=56&h=56&fit=crop&crop=face" alt="Your Profile" class="profile-pic">
                        <div class="profile-details">
                            <h3>john_doe_luxury</h3>
                            <p>John Doe</p>
                        </div>
                    </div>
                    <button class="switch-btn">Switch</button>
                </div>

                <!-- Suggestions -->
                <div class="suggestions-section">
                    <div class="section-header">
                        <h4>Suggested for you</h4>
                        <a href="#" class="see-all">See All</a>
                    </div>
                    <div class="suggestions-list" id="suggestionsList">
                        <!-- Suggestions will be dynamically loaded here -->
                    </div>
                </div>

                <!-- Trending Hashtags -->
                <div class="trending-section">
                    <div class="section-header">
                        <h4>Trending</h4>
                    </div>
                    <div class="trending-list" id="trendingList">
                        <!-- Trending hashtags will be dynamically loaded here -->
                    </div>
                </div>

                <!-- Footer -->
                <footer class="sidebar-footer">
                    <div class="footer-links">
                        <a href="#">About</a>
                        <a href="#">Help</a>
                        <a href="#">Press</a>
                        <a href="#">API</a>
                        <a href="#">Jobs</a>
                        <a href="#">Privacy</a>
                        <a href="#">Terms</a>
                    </div>
                    <p class="copyright">© 2024 Luxgram</p>
                </footer>
            </aside>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal-overlay" id="modalOverlay">
        <!-- Post Creation Modal -->
        <div class="modal create-post-modal" id="createPostModal">
            <div class="modal-header">
                <h3>Create new post</h3>
                <button class="close-btn" id="closeCreateModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <!-- Modal content will be dynamically loaded -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
