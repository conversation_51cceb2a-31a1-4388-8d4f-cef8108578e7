/* ===== SIDEBAR COMPONENTS ===== */
.sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.left-sidebar {
    width: 250px;
    min-width: 250px;
}

.right-sidebar {
    width: 320px;
    min-width: 320px;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background: var(--medium-gray);
    color: var(--text-primary);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    color: var(--primary-black);
    font-weight: 600;
}

.nav-item i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.nav-profile-pic {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
}

/* ===== FEED CONTAINER ===== */
.feed-container {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== STORIES SECTION ===== */
.stories-section {
    margin-bottom: var(--spacing-xl);
}

.stories-container {
    display: flex;
    gap: var(--spacing-md);
    overflow-x: auto;
    padding: var(--spacing-md);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.stories-container::-webkit-scrollbar {
    display: none;
}

.story-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 80px;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.story-item:hover {
    transform: scale(1.05);
}

.story-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-full);
    padding: 3px;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    position: relative;
}

.story-avatar.viewed {
    background: var(--text-gray);
}

.story-avatar img {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--primary-black);
}

.story-username {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.add-story {
    position: relative;
}

.add-story-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: var(--primary-gold);
    color: var(--primary-black);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

/* ===== POST COMPONENTS ===== */
.post-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    transition: transform var(--transition-normal);
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
}

.post-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
    object-fit: cover;
}

.post-user-details h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.post-user-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

.post-menu-btn {
    color: var(--text-muted);
    font-size: 1.25rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.post-menu-btn:hover {
    color: var(--primary-gold);
    background: var(--medium-gray);
}

.post-image {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
    cursor: pointer;
    transition: transform var(--transition-slow);
}

.post-image:hover {
    transform: scale(1.02);
}

.post-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
}

.post-actions-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    color: var(--text-primary);
    font-size: 1.5rem;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.action-btn:hover {
    color: var(--primary-gold);
    transform: scale(1.1);
}

.action-btn.liked {
    color: #ff3040;
}

.action-btn.saved {
    color: var(--primary-gold);
}

.post-stats {
    padding: 0 var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.likes-count {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.post-caption {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.post-caption .username {
    font-weight: 600;
    margin-right: var(--spacing-xs);
}

.view-comments {
    color: var(--text-muted);
    font-size: 0.9rem;
    cursor: pointer;
    margin-bottom: var(--spacing-xs);
}

.view-comments:hover {
    color: var(--text-secondary);
}

.post-time {
    color: var(--text-muted);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.comment-section {
    border-top: 1px solid var(--glass-border);
    padding: var(--spacing-md);
}

.add-comment {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.add-comment input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
}

.add-comment input::placeholder {
    color: var(--text-muted);
}

.post-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.9rem;
    opacity: 0.5;
    transition: opacity var(--transition-fast);
}

.post-btn.active {
    opacity: 1;
    cursor: pointer;
}

/* ===== RIGHT SIDEBAR COMPONENTS ===== */
.profile-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.profile-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.profile-pic {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-gold);
    object-fit: cover;
}

.profile-details h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.profile-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

.switch-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.switch-btn:hover {
    background: var(--medium-gray);
}

.suggestions-section,
.trending-section {
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.section-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-muted);
    margin: 0;
}

.see-all {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.suggestion-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.suggestion-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 1px solid var(--glass-border);
    object-fit: cover;
}

.suggestion-details h5 {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.suggestion-details p {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

.follow-btn {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.follow-btn:hover {
    background: var(--medium-gray);
}

.trending-item {
    padding: var(--spacing-xs) 0;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.trending-item:hover {
    color: var(--primary-gold);
}

.hashtag {
    color: var(--primary-gold);
    font-weight: 600;
    margin-right: var(--spacing-xs);
}

.sidebar-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-links a {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.copyright {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}
