// Main JavaScript file for Instagram clone functionality

// Global state
let currentUser = {
    id: 0,
    username: 'john_doe_luxury',
    fullName: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face'
};

let posts = [...window.sampleData.posts];
let stories = [...window.sampleData.stories];
let comments = [...window.sampleData.comments];
let notifications = [...window.sampleData.notifications];
let messages = [...window.sampleData.messages];
let isLoading = false;

// DOM Elements
const storiesContainer = document.getElementById('storiesContainer');
const postsFeed = document.getElementById('postsFeed');
const suggestionsList = document.getElementById('suggestionsList');
const trendingList = document.getElementById('trendingList');
const modalOverlay = document.getElementById('modalOverlay');
const leftSidebar = document.getElementById('leftSidebar');
const mobileMenuToggle = document.getElementById('mobileMenuToggle');

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadContent();
});

function initializeApp() {
    console.log('🌟 Luxgram initialized');
    
    // Add loading spinner styles
    addLoadingStyles();
    
    // Add empty state styles
    addEmptyStateStyles();
    
    // Setup intersection observer for infinite scroll
    setupInfiniteScroll();
}

function setupEventListeners() {
    // Mobile menu toggle
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }
    
    // Modal overlay click to close
    if (modalOverlay) {
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                closeAllModals();
            }
        });
    }
    
    // Navigation items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.dataset.page;
            if (page) {
                navigateToPage(page);
            }
        });
    });
    
    // Header buttons
    document.getElementById('homeBtn')?.addEventListener('click', () => navigateToPage('home'));
    document.getElementById('messagesBtn')?.addEventListener('click', () => navigateToPage('messages'));
    document.getElementById('addPostBtn')?.addEventListener('click', () => openCreatePostModal());
    document.getElementById('exploreBtn')?.addEventListener('click', () => navigateToPage('explore'));
    document.getElementById('notificationsBtn')?.addEventListener('click', () => navigateToPage('notifications'));
    document.getElementById('profileBtn')?.addEventListener('click', () => navigateToPage('profile'));
    
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        searchInput.addEventListener('focus', showSearchSuggestions);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Window resize handler
    window.addEventListener('resize', handleWindowResize);
}

function loadContent() {
    loadStories();
    loadPosts();
    loadSuggestions();
    loadTrending();
}

// Stories functionality
function loadStories() {
    if (!storiesContainer) return;
    
    let storiesHTML = UIComponents.createStoryItem(null, true); // Add story button
    
    stories.forEach(story => {
        storiesHTML += UIComponents.createStoryItem(story);
    });
    
    storiesContainer.innerHTML = storiesHTML;
    
    // Add horizontal scroll functionality
    setupHorizontalScroll(storiesContainer);
}

function setupHorizontalScroll(container) {
    let isDown = false;
    let startX;
    let scrollLeft;
    
    container.addEventListener('mousedown', (e) => {
        isDown = true;
        container.classList.add('active');
        startX = e.pageX - container.offsetLeft;
        scrollLeft = container.scrollLeft;
    });
    
    container.addEventListener('mouseleave', () => {
        isDown = false;
        container.classList.remove('active');
    });
    
    container.addEventListener('mouseup', () => {
        isDown = false;
        container.classList.remove('active');
    });
    
    container.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - container.offsetLeft;
        const walk = (x - startX) * 2;
        container.scrollLeft = scrollLeft - walk;
    });
}

// Posts functionality
function loadPosts() {
    if (!postsFeed) return;
    
    if (posts.length === 0) {
        postsFeed.innerHTML = UIComponents.createEmptyState(
            'camera',
            'No posts yet',
            'Start following people to see their posts in your feed.',
            'Explore',
            'navigateToPage("explore")'
        );
        return;
    }
    
    let postsHTML = '';
    posts.forEach(post => {
        postsHTML += UIComponents.createPostCard(post);
    });
    
    postsFeed.innerHTML = postsHTML;
}

// Post interactions
function toggleLike(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;

    const likeBtn = document.querySelector(`[data-post-id="${postId}"].action-btn`);
    const likesCount = document.getElementById(`likes-${postId}`);

    if (post.isLiked) {
        post.likes--;
        post.isLiked = false;
        likeBtn.classList.remove('liked');
        UIComponents.createToast('Post unliked', 'info');
    } else {
        post.likes++;
        post.isLiked = true;
        likeBtn.classList.add('liked');
        AnimationUtils.heartAnimation(likeBtn);
        UIComponents.createToast('Post liked! ❤️', 'success');

        // Add notification to post owner (if not own post)
        if (post.userId !== currentUser.id) {
            addNotification('like', post.userId, postId, post.image, 'liked your post');
            updateNotificationBadge();
        }
    }

    likesCount.innerHTML = `<strong>${window.sampleData.formatNumber(post.likes)} likes</strong>`;
}

function toggleSave(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    const saveBtn = document.querySelector(`[data-post-id="${postId}"].action-btn:last-child`);
    
    if (post.isSaved) {
        post.isSaved = false;
        saveBtn.classList.remove('saved');
        UIComponents.createToast('Post removed from saved', 'info');
    } else {
        post.isSaved = true;
        saveBtn.classList.add('saved');
        UIComponents.createToast('Post saved! 📌', 'success');
    }
}

function sharePost(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;
    
    // Create share modal content
    const shareContent = `
        <div class="share-options">
            <h4>Share this post</h4>
            <div class="share-methods">
                <button class="share-btn" onclick="shareToStory(${postId})">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add to story</span>
                </button>
                <button class="share-btn" onclick="sendDirect(${postId})">
                    <i class="fas fa-paper-plane"></i>
                    <span>Send in direct</span>
                </button>
                <button class="share-btn" onclick="copyLink(${postId})">
                    <i class="fas fa-link"></i>
                    <span>Copy link</span>
                </button>
                <button class="share-btn" onclick="shareExternal(${postId})">
                    <i class="fas fa-share"></i>
                    <span>Share to...</span>
                </button>
            </div>
        </div>
    `;
    
    showModal('shareModal', 'Share', shareContent);
}

function showComments(postId) {
    const post = posts.find(p => p.id === postId);
    if (!post) return;

    // Get comments for this post
    const postComments = comments.filter(c => c.postId === postId);

    // Create comments list HTML
    let commentsListHTML = `
        <div class="comment-item original-post">
            <img src="${post.userAvatar}" alt="${post.username}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-text">
                    <strong>${post.username}</strong> ${post.caption}
                </div>
                <div class="comment-meta">
                    <span>${window.sampleData.formatTimeAgo(post.timestamp)}</span>
                </div>
            </div>
        </div>
    `;

    postComments.forEach(comment => {
        commentsListHTML += createCommentHTML(comment);

        // Add replies if any
        if (comment.replies && comment.replies.length > 0) {
            comment.replies.forEach(reply => {
                commentsListHTML += createReplyHTML(reply, comment.id);
            });
        }
    });

    // Create comments modal content
    const commentsContent = `
        <div class="comments-container">
            <div class="post-preview">
                <img src="${post.image}" alt="Post" class="comment-post-image">
            </div>
            <div class="comments-section">
                <div class="comments-header">
                    <div class="post-user-info">
                        <img src="${post.userAvatar}" alt="${post.username}" class="post-avatar">
                        <div class="post-user-details">
                            <h4>${post.username}</h4>
                            <p>${post.location || ''}</p>
                        </div>
                    </div>
                </div>
                <div class="comments-list" id="commentsList-${postId}">
                    ${commentsListHTML}
                </div>
                <div class="add-comment-modal">
                    <input type="text" placeholder="Add a comment..." id="modalCommentInput-${postId}" onkeypress="handleModalCommentSubmit(event, ${postId})">
                    <button class="post-btn" onclick="submitModalComment(${postId})">Post</button>
                </div>
            </div>
        </div>
    `;

    showModal('commentsModal', 'Comments', commentsContent, 'comments-modal');
}

function createCommentHTML(comment) {
    return `
        <div class="comment-item" data-comment-id="${comment.id}">
            <img src="${comment.userAvatar}" alt="${comment.username}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-text">
                    <strong>${comment.username}</strong> ${comment.text}
                </div>
                <div class="comment-meta">
                    <span>${window.sampleData.formatTimeAgo(comment.timestamp)}</span>
                    <button class="comment-action" onclick="likeComment(${comment.id})">
                        <i class="fas fa-heart ${comment.isLiked ? 'liked' : ''}"></i>
                        ${comment.likes > 0 ? comment.likes : ''}
                    </button>
                    <button class="comment-action" onclick="replyToComment(${comment.id})">Reply</button>
                </div>
            </div>
        </div>
    `;
}

function createReplyHTML(reply, parentCommentId) {
    return `
        <div class="comment-item reply" data-reply-id="${reply.id}" data-parent-id="${parentCommentId}">
            <img src="${reply.userAvatar}" alt="${reply.username}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-text">
                    <strong>${reply.username}</strong> ${reply.text}
                </div>
                <div class="comment-meta">
                    <span>${window.sampleData.formatTimeAgo(reply.timestamp)}</span>
                    <button class="comment-action" onclick="likeReply(${reply.id})">
                        <i class="fas fa-heart ${reply.isLiked ? 'liked' : ''}"></i>
                        ${reply.likes > 0 ? reply.likes : ''}
                    </button>
                </div>
            </div>
        </div>
    `;
}

function likeComment(commentId) {
    const comment = comments.find(c => c.id === commentId);
    if (!comment) return;

    const likeBtn = document.querySelector(`[data-comment-id="${commentId}"] .comment-action i.fa-heart`);
    const likeCount = likeBtn.nextSibling;

    if (comment.isLiked) {
        comment.likes--;
        comment.isLiked = false;
        likeBtn.classList.remove('liked');
    } else {
        comment.likes++;
        comment.isLiked = true;
        likeBtn.classList.add('liked');
        AnimationUtils.heartAnimation(likeBtn);

        // Add notification
        addNotification('comment_like', comment.userId, null, null, `liked your comment`);
    }

    likeBtn.nextSibling.textContent = comment.likes > 0 ? comment.likes : '';
}

function likeReply(replyId) {
    let reply = null;
    let parentComment = null;

    // Find the reply in comments
    for (let comment of comments) {
        if (comment.replies) {
            const foundReply = comment.replies.find(r => r.id === replyId);
            if (foundReply) {
                reply = foundReply;
                parentComment = comment;
                break;
            }
        }
    }

    if (!reply) return;

    const likeBtn = document.querySelector(`[data-reply-id="${replyId}"] .comment-action i.fa-heart`);
    const likeCount = likeBtn.nextSibling;

    if (reply.isLiked) {
        reply.likes--;
        reply.isLiked = false;
        likeBtn.classList.remove('liked');
    } else {
        reply.likes++;
        reply.isLiked = true;
        likeBtn.classList.add('liked');
        AnimationUtils.heartAnimation(likeBtn);

        // Add notification
        addNotification('comment_like', reply.userId, null, null, `liked your reply`);
    }

    likeBtn.nextSibling.textContent = reply.likes > 0 ? reply.likes : '';
}

function replyToComment(commentId) {
    const input = document.querySelector('.add-comment-modal input');
    const comment = comments.find(c => c.id === commentId);

    if (input && comment) {
        input.placeholder = `Reply to ${comment.username}...`;
        input.focus();
        input.dataset.replyTo = commentId;
    }
}

// Suggestions and trending
function loadSuggestions() {
    if (!suggestionsList) return;

    let suggestionsHTML = '';
    window.sampleData.suggestions.forEach(suggestion => {
        suggestionsHTML += UIComponents.createSuggestionItem(suggestion);
    });

    suggestionsList.innerHTML = suggestionsHTML;
}

function loadTrending() {
    if (!trendingList) return;

    let trendingHTML = '';
    window.sampleData.trending.forEach(hashtag => {
        trendingHTML += UIComponents.createTrendingItem(hashtag);
    });

    trendingList.innerHTML = trendingHTML;
}

// Modal functionality
function showModal(id, title, content, className = '') {
    const modal = UIComponents.createModal(id, title, content, className);
    modalOverlay.innerHTML = modal;
    modalOverlay.style.display = 'flex';
    AnimationUtils.fadeIn(modalOverlay);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeModal(id) {
    AnimationUtils.fadeOut(modalOverlay);
    setTimeout(() => {
        modalOverlay.style.display = 'none';
        modalOverlay.innerHTML = '';
        document.body.style.overflow = '';
    }, 300);
}

function closeAllModals() {
    closeModal();
}

// Navigation
function navigateToPage(page) {
    // Update active nav item
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.page === page) {
            item.classList.add('active');
        }
    });

    // Handle page-specific logic
    switch (page) {
        case 'home':
            loadPosts();
            UIComponents.createToast('Welcome home! 🏠', 'success');
            break;
        case 'search':
            showSearchPage();
            break;
        case 'explore':
            showExplorePage();
            break;
        case 'reels':
            showReelsPage();
            break;
        case 'messages':
            showMessagesPage();
            break;
        case 'notifications':
            showNotificationsPage();
            break;
        case 'create':
            openCreatePostModal();
            break;
        case 'profile':
            showProfilePage();
            break;
    }
}

// Mobile menu
function toggleMobileMenu() {
    const overlay = document.querySelector('.mobile-overlay') || createMobileOverlay();

    if (leftSidebar.classList.contains('active')) {
        leftSidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    } else {
        leftSidebar.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function createMobileOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'mobile-overlay';
    overlay.addEventListener('click', toggleMobileMenu);
    document.body.appendChild(overlay);
    return overlay;
}

// Search functionality
function handleSearch(event) {
    const query = event.target.value.trim();
    if (query.length < 2) return;

    console.log('Searching for:', query);
    // Implement search logic here
    UIComponents.createToast(`Searching for "${query}"...`, 'info');
}

function showSearchSuggestions() {
    // Show search suggestions dropdown
    console.log('Showing search suggestions');
}

function searchHashtag(tag) {
    console.log('Searching hashtag:', tag);
    UIComponents.createToast(`Searching #${tag}`, 'info');
}

// Create post modal
function openCreatePostModal() {
    const createContent = `
        <div class="create-post-container">
            <div class="create-step" id="createStep1">
                <div class="upload-area" onclick="triggerFileUpload()">
                    <i class="fas fa-camera"></i>
                    <h3>Select photos and videos</h3>
                    <p>Drag photos and videos here</p>
                    <button class="btn btn-primary">Select from computer</button>
                </div>
                <input type="file" id="fileInput" accept="image/*,video/*" multiple style="display: none;" onchange="handleFileSelect(event)">
            </div>
            <div class="create-step" id="createStep2" style="display: none;">
                <div class="image-editor">
                    <div class="image-preview-container">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="image-controls">
                            <button class="control-btn" onclick="rotateImage()">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="control-btn" onclick="cropImage()">
                                <i class="fas fa-crop"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filters-section">
                        <h4>Filters</h4>
                        <div class="filters-list">
                            <div class="filter-item active" data-filter="none" onclick="applyFilter('none')">
                                <div class="filter-preview">Original</div>
                            </div>
                            <div class="filter-item" data-filter="vintage" onclick="applyFilter('vintage')">
                                <div class="filter-preview">Vintage</div>
                            </div>
                            <div class="filter-item" data-filter="gold" onclick="applyFilter('gold')">
                                <div class="filter-preview">Gold</div>
                            </div>
                            <div class="filter-item" data-filter="luxury" onclick="applyFilter('luxury')">
                                <div class="filter-preview">Luxury</div>
                            </div>
                            <div class="filter-item" data-filter="noir" onclick="applyFilter('noir')">
                                <div class="filter-preview">Noir</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="post-details">
                    <div class="user-info">
                        <img src="${currentUser.avatar}" alt="${currentUser.username}" class="user-avatar">
                        <span>${currentUser.username}</span>
                    </div>
                    <textarea placeholder="Write a caption..." id="captionInput" maxlength="2200"></textarea>
                    <div class="caption-counter">
                        <span id="captionCount">0</span>/2,200
                    </div>
                    <input type="text" placeholder="Add location" id="locationInput">
                    <div class="accessibility-section">
                        <h5>Accessibility</h5>
                        <input type="text" placeholder="Write alt text..." id="altTextInput">
                        <p class="help-text">Alt text describes your photos for people with visual impairments.</p>
                    </div>
                    <div class="advanced-settings">
                        <h5>Advanced settings</h5>
                        <div class="post-options">
                            <label class="option-item">
                                <input type="checkbox" id="hideComments">
                                <span>Hide like and view counts on this post</span>
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="turnOffComments">
                                <span>Turn off commenting</span>
                            </label>
                        </div>
                    </div>
                    <div class="publish-section">
                        <button class="btn btn-secondary" onclick="goBackToStep1()">Back</button>
                        <button class="btn btn-primary" onclick="publishPost()" id="publishBtn">Share</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    showModal('createPostModal', 'Create new post', createContent, 'create-post-modal');
    setupCreatePostListeners();
}

function setupCreatePostListeners() {
    // Caption counter
    const captionInput = document.getElementById('captionInput');
    const captionCount = document.getElementById('captionCount');

    if (captionInput && captionCount) {
        captionInput.addEventListener('input', function() {
            captionCount.textContent = this.value.length;

            if (this.value.length > 2200) {
                this.value = this.value.substring(0, 2200);
                captionCount.textContent = 2200;
            }
        });
    }

    // Drag and drop functionality
    const uploadArea = document.querySelector('.upload-area');
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
    }
}

function triggerFileUpload() {
    document.getElementById('fileInput').click();
}

function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0) {
        const file = files[0];
        processSelectedFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
}

function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        processSelectedFile(file);
    }
}

function processSelectedFile(file) {
    if (!file.type.startsWith('image/')) {
        UIComponents.createToast('Please select an image file', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const previewImage = document.getElementById('previewImage');
        if (previewImage) {
            previewImage.src = e.target.result;
            previewImage.dataset.originalSrc = e.target.result;

            // Move to step 2
            document.getElementById('createStep1').style.display = 'none';
            document.getElementById('createStep2').style.display = 'block';

            // Update modal title
            const modalHeader = document.querySelector('.modal-header h3');
            if (modalHeader) {
                modalHeader.textContent = 'Edit post';
            }
        }
    };
    reader.readAsDataURL(file);
}

let currentFilter = 'none';
let imageRotation = 0;

function applyFilter(filterName) {
    const previewImage = document.getElementById('previewImage');
    const filterItems = document.querySelectorAll('.filter-item');

    // Update active filter
    filterItems.forEach(item => item.classList.remove('active'));
    document.querySelector(`[data-filter="${filterName}"]`).classList.add('active');

    currentFilter = filterName;

    // Apply filter styles
    let filterStyle = '';
    switch (filterName) {
        case 'vintage':
            filterStyle = 'sepia(0.8) contrast(1.2) brightness(1.1)';
            break;
        case 'gold':
            filterStyle = 'sepia(0.5) saturate(1.5) hue-rotate(30deg) brightness(1.1)';
            break;
        case 'luxury':
            filterStyle = 'contrast(1.3) brightness(0.9) saturate(1.2)';
            break;
        case 'noir':
            filterStyle = 'grayscale(1) contrast(1.2) brightness(0.9)';
            break;
        default:
            filterStyle = 'none';
    }

    previewImage.style.filter = filterStyle;
    previewImage.style.transform = `rotate(${imageRotation}deg)`;
}

function rotateImage() {
    imageRotation += 90;
    if (imageRotation >= 360) imageRotation = 0;

    const previewImage = document.getElementById('previewImage');
    previewImage.style.transform = `rotate(${imageRotation}deg)`;
}

function cropImage() {
    UIComponents.createToast('Crop functionality - Coming soon! ✂️', 'info');
}

function goBackToStep1() {
    document.getElementById('createStep2').style.display = 'none';
    document.getElementById('createStep1').style.display = 'block';

    // Update modal title
    const modalHeader = document.querySelector('.modal-header h3');
    if (modalHeader) {
        modalHeader.textContent = 'Create new post';
    }

    // Reset form
    document.getElementById('captionInput').value = '';
    document.getElementById('locationInput').value = '';
    document.getElementById('altTextInput').value = '';
    document.getElementById('captionCount').textContent = '0';
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function handleKeyboardShortcuts(event) {
    // Escape key to close modals
    if (event.key === 'Escape') {
        closeAllModals();
        if (leftSidebar.classList.contains('active')) {
            toggleMobileMenu();
        }
    }

    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        document.getElementById('searchInput')?.focus();
    }
}

function handleWindowResize() {
    // Handle responsive behavior on window resize
    if (window.innerWidth > 768 && leftSidebar.classList.contains('active')) {
        toggleMobileMenu();
    }
}

// Additional functionality
function setupInfiniteScroll() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !isLoading) {
                loadMorePosts();
            }
        });
    }, { threshold: 0.1 });

    // Create a sentinel element at the bottom of the feed
    const sentinel = document.createElement('div');
    sentinel.id = 'scroll-sentinel';
    sentinel.style.height = '20px';

    if (postsFeed) {
        postsFeed.appendChild(sentinel);
        observer.observe(sentinel);
    }
}

function loadMorePosts() {
    if (isLoading) return;

    isLoading = true;
    const loadingSpinner = document.createElement('div');
    loadingSpinner.innerHTML = UIComponents.createLoadingSpinner();
    loadingSpinner.id = 'loading-more';
    postsFeed.appendChild(loadingSpinner);

    // Simulate API call
    setTimeout(() => {
        // Add more sample posts (duplicate existing ones for demo)
        const morePosts = window.sampleData.posts.map(post => ({
            ...post,
            id: post.id + posts.length,
            timestamp: new Date(Date.now() - Math.random() * 86400000) // Random time within last day
        }));

        posts.push(...morePosts);

        // Remove loading spinner
        document.getElementById('loading-more')?.remove();

        // Add new posts to feed
        let newPostsHTML = '';
        morePosts.forEach(post => {
            newPostsHTML += UIComponents.createPostCard(post);
        });

        const sentinel = document.getElementById('scroll-sentinel');
        sentinel.insertAdjacentHTML('beforebegin', newPostsHTML);

        isLoading = false;
        UIComponents.createToast('New posts loaded! 📱', 'success');
    }, 1500);
}

// Page-specific functions
function showSearchPage() {
    UIComponents.createToast('Search page - Coming soon! 🔍', 'info');
}

function showExplorePage() {
    UIComponents.createToast('Explore page - Coming soon! 🧭', 'info');
}

function showReelsPage() {
    UIComponents.createToast('Reels page - Coming soon! 🎬', 'info');
}

function showMessagesPage() {
    const messagesHTML = createMessagesHTML();

    const messagesContent = `
        <div class="messages-page">
            <div class="messages-sidebar">
                <div class="messages-header">
                    <h3>${currentUser.username}</h3>
                    <button class="new-message-btn" onclick="showNewMessageModal()">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                <div class="messages-list">
                    ${messagesHTML}
                </div>
            </div>
            <div class="chat-area">
                <div class="no-chat-selected">
                    <i class="fas fa-paper-plane"></i>
                    <h3>Your Messages</h3>
                    <p>Send private photos and messages to a friend or group.</p>
                    <button class="btn btn-primary" onclick="showNewMessageModal()">Send Message</button>
                </div>
            </div>
        </div>
    `;

    showModal('messagesModal', 'Messages', messagesContent, 'messages-modal');
}

function createMessagesHTML() {
    if (messages.length === 0) {
        return `
            <div class="empty-messages">
                <p>No messages yet</p>
                <button class="btn btn-secondary" onclick="showNewMessageModal()">Send your first message</button>
            </div>
        `;
    }

    let messagesHTML = '';
    messages.forEach(conversation => {
        const otherUserId = conversation.participants.find(id => id !== currentUser.id);
        const otherUser = window.sampleData.allUsers.find(u => u.id === otherUserId);

        if (otherUser) {
            const isUnread = !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== currentUser.id;
            messagesHTML += createConversationHTML(conversation, otherUser, isUnread);
        }
    });

    return messagesHTML;
}

function createConversationHTML(conversation, otherUser, isUnread) {
    const timeAgo = window.sampleData.formatTimeAgo(conversation.lastMessage.timestamp);
    const lastMessagePreview = conversation.lastMessage.text.length > 30
        ? conversation.lastMessage.text.substring(0, 30) + '...'
        : conversation.lastMessage.text;

    return `
        <div class="conversation-item ${isUnread ? 'unread' : ''}" onclick="openChat(${conversation.id}, ${otherUser.id})">
            <div class="conversation-avatar">
                <img src="${otherUser.avatar}" alt="${otherUser.username}">
                ${isUnread ? '<div class="unread-indicator"></div>' : ''}
            </div>
            <div class="conversation-info">
                <div class="conversation-header">
                    <h4>${otherUser.username}</h4>
                    <span class="conversation-time">${timeAgo}</span>
                </div>
                <div class="conversation-preview">
                    <span class="${isUnread ? 'unread-text' : ''}">${lastMessagePreview}</span>
                </div>
            </div>
        </div>
    `;
}

function openChat(conversationId, otherUserId) {
    const conversation = messages.find(c => c.id === conversationId);
    const otherUser = window.sampleData.allUsers.find(u => u.id === otherUserId);

    if (!conversation || !otherUser) return;

    // Mark messages as read
    conversation.messages.forEach(msg => {
        if (msg.senderId !== currentUser.id) {
            msg.isRead = true;
        }
    });
    conversation.lastMessage.isRead = true;

    const chatHTML = createChatHTML(conversation, otherUser);

    const chatArea = document.querySelector('.chat-area');
    if (chatArea) {
        chatArea.innerHTML = chatHTML;
    }

    // Scroll to bottom of messages
    setTimeout(() => {
        const messagesContainer = document.querySelector('.chat-messages');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }, 100);
}

function createChatHTML(conversation, otherUser) {
    let messagesHTML = '';
    conversation.messages.forEach(message => {
        const isOwn = message.senderId === currentUser.id;
        const sender = isOwn ? currentUser : otherUser;
        messagesHTML += createMessageHTML(message, sender, isOwn);
    });

    return `
        <div class="chat-header">
            <div class="chat-user-info">
                <img src="${otherUser.avatar}" alt="${otherUser.username}" class="chat-avatar">
                <div class="chat-user-details">
                    <h4>${otherUser.username}</h4>
                    <p>${otherUser.fullName}</p>
                </div>
            </div>
            <div class="chat-actions">
                <button class="chat-action-btn" onclick="callUser(${otherUser.id})">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="chat-action-btn" onclick="videoCallUser(${otherUser.id})">
                    <i class="fas fa-video"></i>
                </button>
                <button class="chat-action-btn" onclick="showChatInfo(${otherUser.id})">
                    <i class="fas fa-info-circle"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chatMessages-${conversation.id}">
            ${messagesHTML}
        </div>
        <div class="chat-input-area">
            <div class="chat-input-container">
                <button class="chat-emoji-btn" onclick="showEmojiPicker()">
                    <i class="fas fa-smile"></i>
                </button>
                <input type="text" placeholder="Message..." class="chat-input"
                       onkeypress="handleChatMessageSubmit(event, ${conversation.id})"
                       id="chatInput-${conversation.id}">
                <button class="chat-send-btn" onclick="sendChatMessage(${conversation.id})">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    `;
}

function createMessageHTML(message, sender, isOwn) {
    const timeAgo = window.sampleData.formatTimeAgo(message.timestamp);

    return `
        <div class="message-item ${isOwn ? 'own' : 'other'}">
            ${!isOwn ? `<img src="${sender.avatar}" alt="${sender.username}" class="message-avatar">` : ''}
            <div class="message-content">
                <div class="message-bubble">
                    ${message.text}
                </div>
                <div class="message-time">${timeAgo}</div>
            </div>
        </div>
    `;
}

function handleChatMessageSubmit(event, conversationId) {
    if (event.key === 'Enter') {
        sendChatMessage(conversationId);
    }
}

function sendChatMessage(conversationId) {
    const input = document.getElementById(`chatInput-${conversationId}`);
    const messageText = input.value.trim();

    if (!messageText) return;

    const conversation = messages.find(c => c.id === conversationId);
    if (!conversation) return;

    const otherUserId = conversation.participants.find(id => id !== currentUser.id);

    // Create new message
    const newMessage = {
        id: Date.now(),
        senderId: currentUser.id,
        text: messageText,
        timestamp: new Date(),
        isRead: false
    };

    // Add to conversation
    conversation.messages.push(newMessage);
    conversation.lastMessage = newMessage;

    // Add notification to other user
    addNotification('message', otherUserId, null, null, 'sent you a message', messageText);
    updateNotificationBadge();

    // Clear input
    input.value = '';

    // Refresh chat display
    const otherUser = window.sampleData.allUsers.find(u => u.id === otherUserId);
    if (otherUser) {
        openChat(conversationId, otherUserId);
    }

    UIComponents.createToast('Message sent! 📩', 'success');
}

function showNewMessageModal() {
    const usersHTML = createNewMessageUsersHTML();

    const newMessageContent = `
        <div class="new-message-container">
            <div class="new-message-header">
                <h4>New Message</h4>
                <div class="search-users">
                    <input type="text" placeholder="Search..." onkeyup="searchUsers(this.value)">
                </div>
            </div>
            <div class="users-list" id="newMessageUsersList">
                ${usersHTML}
            </div>
        </div>
    `;

    showModal('newMessageModal', 'New Message', newMessageContent, 'new-message-modal');
}

function createNewMessageUsersHTML() {
    let usersHTML = '';
    window.sampleData.allUsers.forEach(user => {
        if (user.id !== currentUser.id) {
            usersHTML += `
                <div class="user-item" onclick="startNewConversation(${user.id})">
                    <img src="${user.avatar}" alt="${user.username}" class="user-avatar">
                    <div class="user-info">
                        <h5>${user.username}</h5>
                        <p>${user.fullName}</p>
                    </div>
                </div>
            `;
        }
    });
    return usersHTML;
}

function startNewConversation(userId) {
    // Check if conversation already exists
    const existingConversation = messages.find(c =>
        c.participants.includes(currentUser.id) && c.participants.includes(userId)
    );

    if (existingConversation) {
        closeAllModals();
        showMessagesPage();
        setTimeout(() => openChat(existingConversation.id, userId), 500);
        return;
    }

    // Create new conversation
    const newConversation = {
        id: messages.length + 1,
        participants: [currentUser.id, userId],
        lastMessage: {
            id: Date.now(),
            senderId: currentUser.id,
            text: 'Say hello! 👋',
            timestamp: new Date(),
            isRead: false
        },
        messages: []
    };

    messages.push(newConversation);

    closeAllModals();
    showMessagesPage();
    setTimeout(() => openChat(newConversation.id, userId), 500);
}

// Update the existing showNotificationsPage function is already implemented above

function showProfilePage() {
    UIComponents.createToast('Profile page - Coming soon! 👤', 'info');
}

// Post creation functions
function publishPost() {
    const caption = document.getElementById('captionInput')?.value || '';
    const location = document.getElementById('locationInput')?.value || '';
    const altText = document.getElementById('altTextInput')?.value || '';
    const hideComments = document.getElementById('hideComments')?.checked || false;
    const turnOffComments = document.getElementById('turnOffComments')?.checked || false;
    const previewImage = document.getElementById('previewImage');

    if (!caption.trim()) {
        UIComponents.createToast('Please add a caption', 'error');
        return;
    }

    if (!previewImage || !previewImage.src) {
        UIComponents.createToast('Please select an image', 'error');
        return;
    }

    // Show publishing state
    const publishBtn = document.getElementById('publishBtn');
    publishBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Publishing...';
    publishBtn.disabled = true;

    // Simulate upload delay
    setTimeout(() => {
        // Create new post
        const newPost = {
            id: posts.length + 1,
            userId: currentUser.id,
            username: currentUser.username,
            userAvatar: currentUser.avatar,
            location: location,
            image: previewImage.src, // Use the uploaded image
            caption: caption,
            altText: altText,
            likes: 0,
            comments: 0,
            shares: 0,
            timestamp: new Date(),
            isLiked: false,
            isSaved: false,
            hideComments: hideComments,
            commentsDisabled: turnOffComments,
            filter: currentFilter,
            rotation: imageRotation
        };

        posts.unshift(newPost); // Add to beginning of array
        loadPosts(); // Reload posts
        closeAllModals();

        // Reset create post state
        currentFilter = 'none';
        imageRotation = 0;

        UIComponents.createToast('Post published successfully! 🎉', 'success');

        // Scroll to top to see new post
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 2000);
}

// Comment functions
function handleCommentSubmit(event, postId) {
    if (event.key === 'Enter') {
        submitComment(postId);
    }
}

function handleModalCommentSubmit(event, postId) {
    if (event.key === 'Enter') {
        submitModalComment(postId);
    }
}

function submitComment(postId) {
    const input = event.target.previousElementSibling || document.querySelector(`input[placeholder="Add a comment..."]`);
    const commentText = input.value.trim();

    if (!commentText) return;

    const post = posts.find(p => p.id === postId);
    if (post) {
        // Create new comment
        const newComment = {
            id: comments.length + 1,
            postId: postId,
            userId: currentUser.id,
            username: currentUser.username,
            userAvatar: currentUser.avatar,
            text: commentText,
            likes: 0,
            timestamp: new Date(),
            isLiked: false,
            replies: []
        };

        comments.push(newComment);
        post.comments++;
        input.value = '';

        // Add notification to post owner
        if (post.userId !== currentUser.id) {
            addNotification('comment', post.userId, postId, post.image, 'commented on your post', commentText);
        }

        UIComponents.createToast('Comment added! 💬', 'success');
        updateNotificationBadge();
    }
}

function submitModalComment(postId) {
    const input = document.getElementById(`modalCommentInput-${postId}`);
    const commentText = input.value.trim();

    if (!commentText) return;

    const post = posts.find(p => p.id === postId);
    if (post) {
        // Check if this is a reply
        const isReply = input.dataset.replyTo;

        if (isReply) {
            // Add as reply
            const parentComment = comments.find(c => c.id === parseInt(isReply));
            if (parentComment) {
                const newReply = {
                    id: Date.now(), // Use timestamp as unique ID for replies
                    userId: currentUser.id,
                    username: currentUser.username,
                    userAvatar: currentUser.avatar,
                    text: commentText,
                    likes: 0,
                    timestamp: new Date(),
                    isLiked: false
                };

                if (!parentComment.replies) {
                    parentComment.replies = [];
                }
                parentComment.replies.push(newReply);

                // Add notification to comment owner
                if (parentComment.userId !== currentUser.id) {
                    addNotification('reply', parentComment.userId, postId, post.image, 'replied to your comment', commentText);
                }

                UIComponents.createToast('Reply added! 💬', 'success');
            }

            // Reset reply state
            input.placeholder = 'Add a comment...';
            delete input.dataset.replyTo;
        } else {
            // Add as regular comment
            const newComment = {
                id: comments.length + 1,
                postId: postId,
                userId: currentUser.id,
                username: currentUser.username,
                userAvatar: currentUser.avatar,
                text: commentText,
                likes: 0,
                timestamp: new Date(),
                isLiked: false,
                replies: []
            };

            comments.push(newComment);
            post.comments++;

            // Add notification to post owner
            if (post.userId !== currentUser.id) {
                addNotification('comment', post.userId, postId, post.image, 'commented on your post', commentText);
            }

            UIComponents.createToast('Comment added! 💬', 'success');
        }

        input.value = '';

        // Refresh comments display
        const commentsList = document.getElementById(`commentsList-${postId}`);
        if (commentsList) {
            showComments(postId); // Refresh the modal
        }

        updateNotificationBadge();
    }
}

// Notification functions
function addNotification(type, targetUserId, postId = null, postImage = null, text, additionalText = null) {
    const newNotification = {
        id: notifications.length + 1,
        type: type,
        userId: currentUser.id,
        username: currentUser.username,
        userAvatar: currentUser.avatar,
        postId: postId,
        postImage: postImage,
        text: text,
        timestamp: new Date(),
        isRead: false
    };

    if (additionalText) {
        if (type === 'comment') {
            newNotification.commentText = additionalText;
        } else if (type === 'message') {
            newNotification.messageText = additionalText;
        }
    }

    notifications.unshift(newNotification);

    // Keep only last 50 notifications
    if (notifications.length > 50) {
        notifications = notifications.slice(0, 50);
    }
}

function updateNotificationBadge() {
    const unreadCount = notifications.filter(n => !n.isRead).length;
    const badge = document.querySelector('.notification-badge');

    if (badge) {
        if (unreadCount > 0) {
            badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
}

function showNotificationsPage() {
    const notificationsHTML = createNotificationsHTML();

    const notificationsContent = `
        <div class="notifications-page">
            <div class="notifications-header">
                <h2>Notifications</h2>
                <button class="mark-all-read-btn" onclick="markAllNotificationsRead()">
                    Mark all as read
                </button>
            </div>
            <div class="notifications-list">
                ${notificationsHTML}
            </div>
        </div>
    `;

    showModal('notificationsModal', 'Notifications', notificationsContent, 'notifications-modal');

    // Mark notifications as read after viewing
    setTimeout(() => {
        markAllNotificationsRead();
    }, 1000);
}

function createNotificationsHTML() {
    if (notifications.length === 0) {
        return UIComponents.createEmptyState(
            'bell',
            'No notifications yet',
            'When someone likes or comments on your posts, you\'ll see it here.',
            null,
            null
        );
    }

    let notificationsHTML = '';
    notifications.forEach(notification => {
        notificationsHTML += createNotificationHTML(notification);
    });

    return notificationsHTML;
}

function createNotificationHTML(notification) {
    const timeAgo = window.sampleData.formatTimeAgo(notification.timestamp);
    const isUnread = !notification.isRead ? 'unread' : '';

    let iconClass = '';
    let actionText = notification.text;

    switch (notification.type) {
        case 'like':
            iconClass = 'fas fa-heart liked';
            break;
        case 'comment':
            iconClass = 'fas fa-comment';
            if (notification.commentText) {
                actionText += `: "${notification.commentText}"`;
            }
            break;
        case 'follow':
            iconClass = 'fas fa-user-plus';
            break;
        case 'message':
            iconClass = 'fas fa-paper-plane';
            if (notification.messageText) {
                actionText += `: "${notification.messageText}"`;
            }
            break;
        case 'reply':
            iconClass = 'fas fa-reply';
            break;
        case 'comment_like':
            iconClass = 'fas fa-heart liked';
            break;
    }

    return `
        <div class="notification-item ${isUnread}" data-notification-id="${notification.id}">
            <div class="notification-avatar">
                <img src="${notification.userAvatar}" alt="${notification.username}">
                <div class="notification-icon">
                    <i class="${iconClass}"></i>
                </div>
            </div>
            <div class="notification-content">
                <div class="notification-text">
                    <strong>${notification.username}</strong> ${actionText}
                </div>
                <div class="notification-time">${timeAgo}</div>
            </div>
            ${notification.postImage ? `
                <div class="notification-post-image">
                    <img src="${notification.postImage}" alt="Post" onclick="viewPost(${notification.postId})">
                </div>
            ` : ''}
        </div>
    `;
}

function markAllNotificationsRead() {
    notifications.forEach(notification => {
        notification.isRead = true;
    });
    updateNotificationBadge();

    // Update UI if notifications modal is open
    const notificationItems = document.querySelectorAll('.notification-item.unread');
    notificationItems.forEach(item => {
        item.classList.remove('unread');
    });
}

// Follow/unfollow functions
function followUser(userId) {
    const user = window.sampleData.allUsers.find(u => u.id === userId);
    if (!user) return;

    // Add notification to the user being followed
    addNotification('follow', userId, null, null, 'started following you');
    updateNotificationBadge();

    UIComponents.createToast(`Following ${user.username}! ✨`, 'success');

    // Remove from suggestions
    const suggestionElement = event.target.closest('.suggestion-item');
    if (suggestionElement) {
        AnimationUtils.slideUp(suggestionElement);
    }
}

// Story functions
function viewStory(storyId) {
    const story = stories.find(s => s.id === storyId);
    if (story) {
        story.isViewed = true;
        loadStories(); // Reload to update visual state
        UIComponents.createToast(`Viewing ${story.username}'s story`, 'info');
    }
}

function openCreateStoryModal() {
    UIComponents.createToast('Create story - Coming soon! 📸', 'info');
}

// Share functions
function shareToStory(postId) {
    UIComponents.createToast('Added to story! 📱', 'success');
    closeAllModals();
}

function sendDirect(postId) {
    UIComponents.createToast('Sent in direct message! 📩', 'success');
    closeAllModals();
}

function copyLink(postId) {
    // Simulate copying link to clipboard
    const link = `https://luxgram.com/p/${postId}`;
    navigator.clipboard?.writeText(link);
    UIComponents.createToast('Link copied to clipboard! 🔗', 'success');
    closeAllModals();
}

function shareExternal(postId) {
    if (navigator.share) {
        navigator.share({
            title: 'Check out this post on Luxgram',
            url: `https://luxgram.com/p/${postId}`
        });
    } else {
        UIComponents.createToast('Sharing - Coming soon! 📤', 'info');
    }
    closeAllModals();
}

// Post menu functions
function showPostMenu(postId) {
    const menuContent = `
        <div class="post-menu">
            <button class="menu-item" onclick="reportPost(${postId})">
                <i class="fas fa-flag"></i>
                <span>Report</span>
            </button>
            <button class="menu-item" onclick="unfollowUser(${postId})">
                <i class="fas fa-user-times"></i>
                <span>Unfollow</span>
            </button>
            <button class="menu-item" onclick="copyPostLink(${postId})">
                <i class="fas fa-link"></i>
                <span>Copy link</span>
            </button>
            <button class="menu-item cancel" onclick="closeAllModals()">
                <span>Cancel</span>
            </button>
        </div>
    `;

    showModal('postMenuModal', '', menuContent, 'post-menu-modal');
}

function reportPost(postId) {
    UIComponents.createToast('Post reported', 'info');
    closeAllModals();
}

function unfollowUser(postId) {
    UIComponents.createToast('User unfollowed', 'info');
    closeAllModals();
}

function copyPostLink(postId) {
    copyLink(postId);
}

// Additional utility functions for messaging and other features
function callUser(userId) {
    const user = window.sampleData.allUsers.find(u => u.id === userId);
    UIComponents.createToast(`Calling ${user?.username}... 📞`, 'info');
}

function videoCallUser(userId) {
    const user = window.sampleData.allUsers.find(u => u.id === userId);
    UIComponents.createToast(`Video calling ${user?.username}... 📹`, 'info');
}

function showChatInfo(userId) {
    const user = window.sampleData.allUsers.find(u => u.id === userId);
    UIComponents.createToast(`Chat info for ${user?.username}`, 'info');
}

function showEmojiPicker() {
    UIComponents.createToast('Emoji picker - Coming soon! 😊', 'info');
}

function searchUsers(query) {
    const usersList = document.getElementById('newMessageUsersList');
    if (!usersList) return;

    const filteredUsers = window.sampleData.allUsers.filter(user =>
        user.id !== currentUser.id &&
        (user.username.toLowerCase().includes(query.toLowerCase()) ||
         user.fullName.toLowerCase().includes(query.toLowerCase()))
    );

    let usersHTML = '';
    filteredUsers.forEach(user => {
        usersHTML += `
            <div class="user-item" onclick="startNewConversation(${user.id})">
                <img src="${user.avatar}" alt="${user.username}" class="user-avatar">
                <div class="user-info">
                    <h5>${user.username}</h5>
                    <p>${user.fullName}</p>
                </div>
            </div>
        `;
    });

    usersList.innerHTML = usersHTML || '<p class="no-results">No users found</p>';
}

function viewPost(postId) {
    const post = posts.find(p => p.id === postId);
    if (post) {
        closeAllModals();
        // Scroll to post (simplified implementation)
        UIComponents.createToast('Viewing post...', 'info');
    }
}

// Initialize notification badge on load
function initializeNotificationBadge() {
    updateNotificationBadge();
}

// Update the initialization to include notification badge
function initializeApp() {
    console.log('🌟 Luxgram initialized');

    // Add loading spinner styles
    addLoadingStyles();

    // Add empty state styles
    addEmptyStateStyles();

    // Setup intersection observer for infinite scroll
    setupInfiniteScroll();

    // Initialize notification badge
    initializeNotificationBadge();
}

// Style injection functions
function addLoadingStyles() {
    if (document.querySelector('#loading-styles')) return;

    const loadingStyles = document.createElement('style');
    loadingStyles.id = 'loading-styles';
    loadingStyles.textContent = `
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--medium-gray);
            border-top: 3px solid var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .spinner-sm .spinner {
            width: 20px;
            height: 20px;
            border-width: 2px;
        }

        .spinner-lg .spinner {
            width: 60px;
            height: 60px;
            border-width: 4px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(loadingStyles);
}

function addEmptyStateStyles() {
    if (document.querySelector('#empty-state-styles')) return;

    const emptyStateStyles = document.createElement('style');
    emptyStateStyles.id = 'empty-state-styles';
    emptyStateStyles.textContent = `
        .empty-state {
            text-align: center;
            padding: var(--spacing-2xl);
            color: var(--text-muted);
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            color: var(--primary-gold);
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-md);
            color: var(--text-secondary);
        }

        .empty-state-description {
            font-size: 1rem;
            margin-bottom: var(--spacing-xl);
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .modal {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--glass-border);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 1.25rem;
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .close-btn:hover {
            color: var(--text-primary);
            background: var(--medium-gray);
        }

        .modal-content {
            padding: var(--spacing-lg);
            overflow-y: auto;
            max-height: calc(80vh - 80px);
        }

        /* Comments modal specific styles */
        .comments-modal {
            max-width: 800px;
            height: 80vh;
        }

        .comments-container {
            display: flex;
            height: 100%;
        }

        .post-preview {
            flex: 1;
            background: var(--primary-black);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .comment-post-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .comments-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-left: 1px solid var(--glass-border);
        }

        .comments-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--glass-border);
        }

        .comments-list {
            flex: 1;
            overflow-y: auto;
            padding: var(--spacing-md);
        }

        .comment-item {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            object-fit: cover;
        }

        .comment-content {
            flex: 1;
        }

        .comment-text {
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: var(--spacing-xs);
        }

        .comment-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .comment-action {
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 0.8rem;
            transition: color var(--transition-fast);
        }

        .comment-action:hover {
            color: var(--text-secondary);
        }

        .add-comment-modal {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            border-top: 1px solid var(--glass-border);
        }

        .add-comment-modal input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 0.9rem;
            outline: none;
        }

        .add-comment-modal input::placeholder {
            color: var(--text-muted);
        }

        /* Share modal styles */
        .share-options h4 {
            text-align: center;
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
        }

        .share-methods {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .share-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: none;
            border: none;
            color: var(--text-primary);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: left;
        }

        .share-btn:hover {
            background: var(--medium-gray);
        }

        .share-btn i {
            font-size: 1.25rem;
            color: var(--primary-gold);
        }

        /* Post menu styles */
        .post-menu-modal .modal {
            max-width: 300px;
        }

        .post-menu {
            display: flex;
            flex-direction: column;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: left;
            border-bottom: 1px solid var(--glass-border);
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: var(--medium-gray);
        }

        .menu-item.cancel {
            color: var(--text-muted);
            justify-content: center;
        }

        .menu-item i {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        /* Create post modal styles */
        .create-post-modal {
            max-width: 800px;
            height: 80vh;
        }

        .create-post-container {
            height: 100%;
        }

        .create-step {
            height: 100%;
        }

        .upload-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            border: 2px dashed var(--glass-border);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .upload-area:hover {
            border-color: var(--primary-gold);
            background: var(--glass-bg);
        }

        .upload-area i {
            font-size: 3rem;
            color: var(--primary-gold);
            margin-bottom: var(--spacing-lg);
        }

        .upload-area h3 {
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .upload-area p {
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
        }
    `;
    document.head.appendChild(emptyStateStyles);
}

// Initialize everything when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Luxgram fully loaded and ready!');
    });
} else {
    console.log('🚀 Luxgram fully loaded and ready!');
}
