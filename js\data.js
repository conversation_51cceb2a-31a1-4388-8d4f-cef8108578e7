// Sample data for the Instagram clone

const sampleUsers = [
    {
        id: 1,
        username: 'luxury_lifestyle',
        fullName: 'Luxury Lifestyle',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        isVerified: true,
        followers: 125000,
        following: 890,
        posts: 342,
        bio: '✨ Living the golden life | 🏆 Luxury enthusiast | 📍 Monaco'
    },
    {
        id: 2,
        username: 'golden_moments',
        fullName: 'Golden Moments',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        isVerified: false,
        followers: 89000,
        following: 1200,
        posts: 156,
        bio: '🌟 Capturing golden memories | 📸 Photographer | ✈️ World traveler'
    },
    {
        id: 3,
        username: 'elite_fashion',
        fullName: 'Elite Fashion',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        isVerified: true,
        followers: 234000,
        following: 567,
        posts: 789,
        bio: '👗 High-end fashion | 💎 Luxury brands | 🛍️ Style curator'
    },
    {
        id: 4,
        username: 'prestige_cars',
        fullName: 'Prestige Cars',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        isVerified: true,
        followers: 456000,
        following: 234,
        posts: 445,
        bio: '🏎️ Luxury automobiles | 🔥 Supercars | 🏁 Racing enthusiast'
    },
    {
        id: 5,
        username: 'diamond_dreams',
        fullName: 'Diamond Dreams',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        isVerified: false,
        followers: 67000,
        following: 890,
        posts: 234,
        bio: '💎 Jewelry designer | ✨ Handcrafted luxury | 🎨 Artist'
    }
];

const sampleStories = [
    {
        id: 1,
        userId: 1,
        username: 'luxury_lifestyle',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        isViewed: false,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
        id: 2,
        userId: 2,
        username: 'golden_moments',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        isViewed: true,
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago
    },
    {
        id: 3,
        userId: 3,
        username: 'elite_fashion',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        isViewed: false,
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000) // 1 hour ago
    },
    {
        id: 4,
        userId: 4,
        username: 'prestige_cars',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        isViewed: false,
        timestamp: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
    },
    {
        id: 5,
        userId: 5,
        username: 'diamond_dreams',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        isViewed: true,
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
    }
];

const samplePosts = [
    {
        id: 1,
        userId: 1,
        username: 'luxury_lifestyle',
        userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        location: 'Monaco, Monte Carlo',
        image: 'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?w=600&h=600&fit=crop',
        caption: 'Living the golden life in Monaco ✨ Nothing beats the luxury of the French Riviera! #luxury #monaco #goldlife #lifestyle',
        likes: 12847,
        comments: 234,
        shares: 45,
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
        isLiked: false,
        isSaved: false
    },
    {
        id: 2,
        userId: 3,
        username: 'elite_fashion',
        userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        location: 'Paris, France',
        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=600&h=600&fit=crop',
        caption: 'New collection dropping soon! 👗✨ Luxury fashion meets timeless elegance. Can\'t wait to share these pieces with you! #fashion #luxury #paris #couture',
        likes: 8934,
        comments: 156,
        shares: 78,
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        isLiked: true,
        isSaved: true
    },
    {
        id: 3,
        userId: 4,
        username: 'prestige_cars',
        userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        location: 'Dubai, UAE',
        image: 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=600&h=600&fit=crop',
        caption: 'Pure automotive perfection 🏎️🔥 This beauty just arrived at our Dubai showroom. The gold accents are absolutely stunning! #supercar #luxury #dubai #automotive',
        likes: 15623,
        comments: 289,
        shares: 134,
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
        isLiked: false,
        isSaved: false
    },
    {
        id: 4,
        userId: 2,
        username: 'golden_moments',
        userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        location: 'Santorini, Greece',
        image: 'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?w=600&h=600&fit=crop',
        caption: 'Golden hour in Santorini 🌅✨ Sometimes the most precious moments can\'t be bought with gold - they\'re captured with the heart. #santorini #goldenhour #travel #photography',
        likes: 6789,
        comments: 98,
        shares: 23,
        timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 hours ago
        isLiked: true,
        isSaved: false
    },
    {
        id: 5,
        userId: 5,
        username: 'diamond_dreams',
        userAvatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        location: 'New York, NY',
        image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=600&h=600&fit=crop',
        caption: 'Handcrafted perfection 💎✨ Each piece tells a story of luxury and elegance. This custom necklace took 3 months to complete! #jewelry #diamonds #handmade #luxury',
        likes: 4567,
        comments: 67,
        shares: 12,
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        isLiked: false,
        isSaved: true
    }
];

const sampleSuggestions = [
    {
        id: 6,
        username: 'royal_watches',
        fullName: 'Royal Timepieces',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 12,
        reason: 'Followed by luxury_lifestyle + 11 others'
    },
    {
        id: 7,
        username: 'platinum_lifestyle',
        fullName: 'Platinum Living',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 8,
        reason: 'Followed by elite_fashion + 7 others'
    },
    {
        id: 8,
        username: 'gold_standard',
        fullName: 'Gold Standard',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 15,
        reason: 'Followed by prestige_cars + 14 others'
    }
];

const trendingHashtags = [
    { tag: 'luxury', posts: 2400000 },
    { tag: 'goldlife', posts: 890000 },
    { tag: 'lifestyle', posts: 5600000 },
    { tag: 'fashion', posts: 8900000 },
    { tag: 'diamonds', posts: 1200000 },
    { tag: 'supercars', posts: 670000 },
    { tag: 'monaco', posts: 450000 },
    { tag: 'elegance', posts: 780000 }
];

// Utility functions
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function formatTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}m`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}h`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days}d`;
    } else {
        const weeks = Math.floor(diffInSeconds / 604800);
        return `${weeks}w`;
    }
}

// Export data for use in other files
window.sampleData = {
    users: sampleUsers,
    stories: sampleStories,
    posts: samplePosts,
    suggestions: sampleSuggestions,
    trending: trendingHashtags,
    formatNumber,
    formatTimeAgo
};
