/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop */
@media (min-width: 1400px) {
    .main-content {
        max-width: 1400px;
    }
    
    .left-sidebar {
        width: 280px;
        min-width: 280px;
    }
    
    .right-sidebar {
        width: 350px;
        min-width: 350px;
    }
}

/* Desktop */
@media (max-width: 1200px) {
    .main-content {
        max-width: 1000px;
        gap: var(--spacing-lg);
    }
    
    .left-sidebar {
        width: 220px;
        min-width: 220px;
    }
    
    .right-sidebar {
        width: 300px;
        min-width: 300px;
    }
}

/* Tablet Landscape */
@media (max-width: 1024px) {
    .main-content {
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .left-sidebar {
        width: 200px;
        min-width: 200px;
    }
    
    .nav-item span {
        font-size: 0.9rem;
    }
    
    .right-sidebar {
        width: 280px;
        min-width: 280px;
    }
    
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .search-container {
        max-width: 300px;
        margin: 0 var(--spacing-lg);
    }
}

/* Tablet Portrait */
@media (max-width: 768px) {
    .header {
        padding: var(--spacing-sm) 0;
    }
    
    .header-content {
        padding: 0 var(--spacing-sm);
    }
    
    .logo span {
        display: none;
    }
    
    .search-container {
        max-width: 250px;
        margin: 0 var(--spacing-md);
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
    
    .main-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        padding: var(--spacing-sm);
        margin-top: 70px;
    }
    
    .left-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1002;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 0;
        border-right: 1px solid var(--glass-border);
        transition: left var(--transition-normal);
        overflow-y: auto;
    }
    
    .left-sidebar.active {
        left: 0;
    }
    
    .right-sidebar {
        width: 100%;
        min-width: auto;
        position: static;
    }
    
    .feed-container {
        width: 100%;
        max-width: none;
    }
    
    .stories-container {
        padding: var(--spacing-sm);
    }
    
    .story-item {
        min-width: 70px;
    }
    
    .story-avatar {
        width: 56px;
        height: 56px;
    }
    
    .post-card {
        margin-bottom: var(--spacing-lg);
    }
}

/* Mobile */
@media (max-width: 480px) {
    .header {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-content {
        margin-top: 0;
        padding: var(--spacing-xs);
        gap: var(--spacing-md);
    }
    
    .left-sidebar {
        width: 100%;
        left: -100%;
    }
    
    .sidebar {
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
    }
    
    .nav-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .nav-item span {
        font-size: 1rem;
    }
    
    .stories-container {
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .story-item {
        min-width: 60px;
    }
    
    .story-avatar {
        width: 48px;
        height: 48px;
    }
    
    .story-username {
        font-size: 0.7rem;
        max-width: 60px;
    }
    
    .post-header {
        padding: var(--spacing-sm);
    }
    
    .post-actions {
        padding: var(--spacing-sm);
    }
    
    .post-stats {
        padding: 0 var(--spacing-sm);
    }
    
    .comment-section {
        padding: var(--spacing-sm);
    }
    
    .action-btn {
        font-size: 1.25rem;
    }
    
    .right-sidebar {
        order: -1;
    }
    
    .profile-summary {
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
    }
    
    .suggestions-section,
    .trending-section {
        margin-bottom: var(--spacing-lg);
    }
    
    .sidebar-footer {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
    }
}

/* Small Mobile */
@media (max-width: 360px) {
    .main-content {
        padding: var(--spacing-xs);
    }
    
    .stories-container {
        padding: var(--spacing-xs);
    }
    
    .story-item {
        min-width: 50px;
    }
    
    .story-avatar {
        width: 40px;
        height: 40px;
    }
    
    .story-username {
        font-size: 0.65rem;
        max-width: 50px;
    }
    
    .post-header,
    .post-actions,
    .comment-section {
        padding: var(--spacing-xs);
    }
    
    .post-stats {
        padding: 0 var(--spacing-xs);
    }
    
    .post-user-info {
        gap: var(--spacing-sm);
    }
    
    .post-avatar {
        width: 32px;
        height: 32px;
    }
    
    .post-user-details h4 {
        font-size: 0.8rem;
    }
    
    .post-user-details p {
        font-size: 0.7rem;
    }
    
    .action-btn {
        font-size: 1.1rem;
        padding: var(--spacing-xs);
    }
    
    .post-actions-left {
        gap: var(--spacing-sm);
    }
}

/* ===== MODAL RESPONSIVE ===== */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        max-width: none;
        margin: var(--spacing-md);
        border-radius: var(--radius-md);
    }
    
    .modal-header {
        padding: var(--spacing-md);
    }
    
    .modal-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .modal {
        width: 100%;
        height: 100%;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }
    
    .modal-header {
        padding: var(--spacing-sm);
        border-bottom: 1px solid var(--glass-border);
    }
    
    .modal-content {
        padding: var(--spacing-sm);
        overflow-y: auto;
        max-height: calc(100vh - 60px);
    }
}

/* ===== UTILITY CLASSES FOR RESPONSIVE ===== */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

@media (max-width: 768px) {
    .hide-mobile {
        display: none;
    }
    
    .show-mobile {
        display: block;
    }
}

.hide-tablet {
    display: block;
}

.show-tablet {
    display: none;
}

@media (max-width: 1024px) {
    .hide-tablet {
        display: none;
    }
    
    .show-tablet {
        display: block;
    }
}

/* ===== OVERLAY FOR MOBILE MENU ===== */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1001;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.mobile-overlay.active {
    display: block;
    opacity: 1;
}

@media (max-width: 768px) {
    .mobile-overlay {
        display: block;
    }
}
